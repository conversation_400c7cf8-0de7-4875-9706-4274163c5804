from dotenv import load_dotenv
import MetaTrader5 as mt5
import os
import json

# ===========================
# Class: Config
# ===========================
class Config:
    def __init__(self):
        load_dotenv()
        self.app_name = "ZD Internal App"
        self.app_key = "zdinternalapp"

        # Debug mode setting
        self.DEBUG = os.getenv('DEBUG', 'false').lower() == 'true'


        # self.account_default = "DEMO1 IUX Std"
        self.port_default = 5050
        self.account_default = "REAL2 CFX Std"
        self.accounts = {
            "DEMO1 IUX Std":"DEMO1",
            "DEMO2 CFX Std":"DEMO2",
            "DEMO3 CFX Ult":"DEMO3",
            "DEMO4 CFX MIC":"DEMO4",
            "REAL1 XMG Std":"REAL1",
            "REAL2 CFX Std":"REAL2",
        }
        # Log level setting (1=Critical, 2=Error, 3=Warning, 4=Info, 5=Debug)
        self.log_level = 3  # Default to Warning level
        self.log_level_names = {
            1: "Critical",
            2: "Error",
            3: "Warning",
            4: "Info",
            5: "Debug"
        }

        # Quiet mode setting (deprecated, use log_level instead)
        self.quiet_mode = False

        # AI Bot configuration (loaded from JSON file)
        self.ai_bots = {}
        self.ai_bots_file = os.path.join(os.getcwd(), "ai_bots.json")
        self.load_ai_bots()

        # AI Bot default settings for new bots
        self.ai_bot_defaults = {
            "symbol": "XU",  # Default to XAUUSD (Gold)
            "timeframes": ["H1"],  # Default timeframes (multi-timeframe support)
            "timeframe": "H1",  # Legacy single timeframe support
            "bars_back": 100,  # Default bars back
            "prompt": "Analyze the XAUUSD chart data and provide trading insights based on technical indicators.",
            "enabled": True,
            "api_provider": "gpt",  # "gpt" or "gemini"
            "auto_place_order": False,  # Auto place orders based on AI analysis
            "schedule_check": False,  # Schedule periodic checks
            "schedule_type": "custom",  # custom, hourly, daily, weekly, monthly
            "schedule_custom_minutes": 60,  # For custom schedule
            "schedule_daily_time": "09:00",  # For daily schedule
            "schedule_weekly_day": "Monday",  # For weekly schedule
            "schedule_weekly_time": "09:00",  # For weekly schedule
            "schedule_monthly_day": 1,  # For monthly schedule (1-31)
            "schedule_monthly_time": "09:00",  # For monthly schedule
            "chart_image_path": "",  # Path to chart image for analysis
            "use_chart_image": False,  # Whether to include chart image in analysis
            "use_signal_format": True,  # Use structured signal format (enabled by default)
            "multi_timeframe": True  # Enable multi-timeframe analysis
        }

        # AI API configuration
        self.ai_api_config = {
            "gpt": {
                "name": "OpenAI GPT",
                "api_key": "",  # Set via environment variable or UI
                "model": "gpt-5",  # Changed from gpt-4 to gpt-3.5-turbo for broader compatibility
                # "model": "gpt-3.5-turbo",  # Changed from gpt-4 to gpt-3.5-turbo for broader compatibility
                "max_tokens": 20000
            },
            "gemini": {
                "name": "Google Gemini",
                "api_key": "",  # Set via environment variable or UI
                "model": "gemini-1.5-flash",  # Updated to latest Gemini model
                "max_tokens": 20000
            }
        }

        # AI Signal Response Format Template
        self.ai_signal_format = {
            "template": """Signal ID: {{signal_id}}
C.GPT
Symbol: {{symbol}}
Signal: {{signal_type}}
Price: {{entry_price}}
SL: {{sl_price}}
TP1: {{tp1_price}}
TP2: {{tp2_price}}
TP3: {{tp3_price}}
TP4: {{tp4_price}}
TP5: {{tp5_price}}
Reason: {{reason}}
Risk: {{risk}}""",

            "format_prompt": """
IMPORTANT: You will respond ONLY in this exact format, no additional text:

Signal ID: [8 random characters a-z0-9]
C.GPT
Symbol: [trading symbol]
Signal: [Buy Limit or Sell Limit]
Price: [entry price with correct decimals for the symbol]
SL: [stop loss price with correct decimals for the symbol]
TP1: [take profit 1 price with correct decimals for the symbol]
TP2: [take profit 2 price with correct decimals for the symbol]
TP3: [take profit 3 price with correct decimals for the symbol]
TP4: [take profit 4 price with correct decimals for the symbol]
TP5: [take profit 5 price with correct decimals for the symbol]
Reason: [50 words max explaining why this signal was generated]
Risk: [50 words max describing risk factors and risk management considerations]

Example:
Signal ID: a7b9c2d4
C.GPT
Symbol: XAUUSD
Signal: Buy Limit
Price: 2045.50
SL: 2040.00
TP1: 2050.00
TP2: 2055.00
TP3: 2060.00
TP4: 2065.00
TP5: 2070.00
Reason: RSI oversold at 25, MACD bullish crossover, price bounced off key support at 2040. EMA20 trending up, confluence of Fibonacci 61.8% retracement and pivot point support suggests strong buying opportunity with favorable risk-reward ratio.
Risk: RSI overbought at 4H, EMA100 trending down on 4H, with a lot of SELL Volume, SL placement below key support minimizes downside. Position size should account for potential gap risk during market open.

Respond with ONLY this format, nothing else. but if no signal at all, respond with "NO SIGNAL" in all caps.
"""
        }
        self.symbol_posfix = None
        self.symbol_default = "XU"
        self.symbols = {
            "XU":"XAUUSD", 
            "BU":"BTCUSD", 
            "U3":"US30cash", 
            "U1":"US100cash", 
            "U5":"US500cash", 
            "AU":"AUDUSD", 
            "AJ":"AUDJPY", 
            "ACD":"AUDCAD", 
            "ACF":"AUDCHF", 
            "EU":"EURUSD", 
            "GN":"GBPNZD",
            "GU":"GBPUSD",
            "UCD":"USDCAD",
            "UCF":"USDCHF",
            "UJ":"USDJPY", 
            "NCF":"NZDCHF", 
            "NJ":"NZDJPY", 
            "NU":"NZDUSD", 
        }
        self.order_type_mapping = {
            "Buy Now": mt5.ORDER_TYPE_BUY,
            "Sell Now": mt5.ORDER_TYPE_SELL,
            "Buy Limit": mt5.ORDER_TYPE_BUY_LIMIT,
            "Sell Limit": mt5.ORDER_TYPE_SELL_LIMIT,
            "Buy Stop": mt5.ORDER_TYPE_BUY_STOP,
            "Sell Stop": mt5.ORDER_TYPE_SELL_STOP,
        }
        self.action_type_mapping = {
            "Buy Now": mt5.TRADE_ACTION_DEAL,
            "Sell Now": mt5.TRADE_ACTION_DEAL,
            "Buy Limit": mt5.TRADE_ACTION_PENDING,
            "Sell Limit": mt5.TRADE_ACTION_PENDING,
            "Buy Stop": mt5.TRADE_ACTION_PENDING,
            "Sell Stop": mt5.TRADE_ACTION_PENDING,
        }
        
        self.timeframes = {
            "M5": mt5.TIMEFRAME_M5,
            "M15": mt5.TIMEFRAME_M15,
            "M30": mt5.TIMEFRAME_M30,
            "H1": mt5.TIMEFRAME_H1,
            "H4": mt5.TIMEFRAME_H4,
            # "D1": mt5.TIMEFRAME_D1
        }
        self.timeframes_start_point = {
            "M5": 1000,
            "M15": 1500,
            "M30": 2000,
            "H1": 2000,
            "H4": 3500,
            # "D1": 5000
        }
        self.timeframes_map_htf = {
            "M5": mt5.TIMEFRAME_H1,
            "M15":  mt5.TIMEFRAME_H4,
            "M30":  mt5.TIMEFRAME_H4,
            "H1":  mt5.TIMEFRAME_H8,
            "H4":  mt5.TIMEFRAME_D1,
            # "D1": 100
        }

        # ไปที่: https://notify-bot.line.me/my/
        # เลือก "Generate token" → ตั้งชื่อ → เลือกกลุ่มที่จะให้แจ้ง
        # คัดลอก Token → เอามาใส่ในโค้ดตรง LINE_TOKEN
        self.LINE_TOKEN = ''

        # Google Form Integration for Order Tracking
        # Pre-fill URL template for sending order data to Google Sheets
        # Parameters: {Symbol}, {Action}, {Entry}, {SL}, {TP}, {Lot}, {SignalID}, {Comment}, {Reason}, {Risk}
        self.GOOGLE_FORM_URL = "https://docs.google.com/forms/d/e/1FAIpQLSda4_GifbWv-MGp9j-2jdbtCYzUlDN-chjhprEMHUG4DkkH_g/viewform?usp=pp_url&entry.178506718={Symbol}&entry.645304246={Action}&entry.785671803={Entry}&entry.898028705={SL}&entry.1523790774={TP}&entry.381285031={Lot}&entry.1073635155={SignalID}&entry.1148525053={Comment}&entry.398055293={Reason}&entry.305299530={Risk}"

        # Google Form field mapping for easy reference
        self.GOOGLE_FORM_FIELDS = {
            "symbol": "entry.178506718",
            "action": "entry.645304246",
            "entry": "entry.785671803",
            "sl": "entry.898028705",  # Stop Loss field
            "tp": "entry.1523790774",
            "lot": "entry.381285031",
            "signal_id": "entry.1073635155",
            "comment": "entry.1148525053",
            "reason": "entry.398055293",
            "risk": "entry.305299530"  # Risk field
        }

        self.MAX_TP_FIELDS = 5
        self.MAX_ORDERS = 1

        self.RSI1_LEN = 25
        self.SMA1_LEN = 50

        self.RSI2_LEN = 50
        self.SMA2_LEN = 25

        self.RSI3_LEN = 25
        self.SMA3_LEN = 25
         
        self.BE_POINTS = 1000
        self.SL_POINTS = 1000
        self.TP1_POINTS = 2000
        self.TP2_POINTS = 3000
        self.TP3_POINTS = 4000
        self.LIMIT_LOGS = 100
        self.status_label_frame = None
        # self.timer_label = None
        self.status_label = None
        self.status_scroll_frame = None
        self.status_scroll_labels = []

        # Order groups configuration for SL and TP toggles
        # Each group has: prefix, display_name, has_group_id, default_sl_enabled, default_tp_enabled, has_subtab, subtab_type
        self.order_groups = {
            "ALL": {
                "prefix": "",
                "display_name": "All",
                "has_group_id": False,
                "default_sl_enabled": False,
                "default_tp_enabled": False,
                "has_subtab": True,
                "subtab_type": "combined",  # combined view of positions and pending orders
                "subtab_name": "All Orders"
            },
            "ZD": {
                "prefix": "ZD",
                "display_name": "ZD",
                "has_group_id": True,
                "default_sl_enabled": False,
                "default_tp_enabled": False,
                "has_subtab": True,
                "subtab_type": "grouped",  # grouped by ID
                "subtab_name": "ZD Orders"
            },
            "IN": {
                "prefix": "IN",
                "display_name": "IN",
                "has_group_id": True,
                "default_sl_enabled": False,
                "default_tp_enabled": False,
                "has_subtab": True,
                "subtab_type": "grouped",  # grouped by ID
                "subtab_name": "IN Orders"
            },
            "WH": {
                "prefix": "WH",
                "display_name": "WH",
                "has_group_id": False,
                "default_sl_enabled": True,
                "default_tp_enabled": False,
                "has_subtab": True,
                "subtab_type": "individual",  # individual orders without grouping
                "subtab_name": "WH Orders"
            },
            # "SG": {
            #     "prefix": "SG",
            #     "display_name": "Signal",
            #     "has_group_id": True,
            #     "default_sl_enabled": False,
            #     "default_tp_enabled": True,
            #     "has_subtab": True,
            #     "subtab_type": "grouped",  # grouped by ID like ZD and IN
            #     "subtab_name": "Signal Orders"
            # }
        }

    # ===========================
    # AI Bots JSON Storage Methods
    # ===========================

    def load_ai_bots(self):
        """Load AI bots from JSON file"""
        try:
            if os.path.exists(self.ai_bots_file):
                with open(self.ai_bots_file, 'r', encoding='utf-8') as f:
                    self.ai_bots = json.load(f)
                print(f"✅ Loaded {len(self.ai_bots)} AI bots from {self.ai_bots_file}")
            else:
                # Create default bot if no file exists
                self.ai_bots = {
                    "bot_1": {
                        "name": "Default XAUUSD Bot",
                        "symbol": "XU",
                        "timeframes": ["H1"],
                        "timeframe": "H1",  # Legacy support
                        "bars_back": 100,
                        "prompt": "Analyze the XAUUSD chart data and provide trading insights based on technical indicators.",
                        "enabled": True,
                        "api_provider": "gpt",
                        "auto_place_order": False,
                        "schedule_check": False,
                        "schedule_type": "custom",
                        "schedule_custom_minutes": 60,
                        "schedule_daily_time": "09:00",
                        "schedule_weekly_day": "Monday",
                        "schedule_weekly_time": "09:00",
                        "schedule_monthly_day": 1,
                        "schedule_monthly_time": "09:00",
                        "chart_image_path": "",
                        "use_chart_image": False,
                        "use_signal_format": True,  # Enabled by default
                        "multi_timeframe": False,
                        "created_at": "2025-01-01 00:00:00",
                        "last_used": None
                    }
                }
                self.save_ai_bots()
                print(f"✅ Created default AI bot and saved to {self.ai_bots_file}")
        except Exception as e:
            print(f"❌ Error loading AI bots: {e}")
            self.ai_bots = {}

    def save_ai_bots(self):
        """Save AI bots to JSON file"""
        try:
            with open(self.ai_bots_file, 'w', encoding='utf-8') as f:
                json.dump(self.ai_bots, f, indent=2, ensure_ascii=False)
            print(f"✅ Saved {len(self.ai_bots)} AI bots to {self.ai_bots_file}")
        except Exception as e:
            print(f"❌ Error saving AI bots: {e}")

    def add_ai_bot(self, bot_id, bot_config):
        """Add a new AI bot and save to file"""
        self.ai_bots[bot_id] = bot_config
        self.save_ai_bots()

    def update_ai_bot(self, bot_id, bot_config):
        """Update an existing AI bot and save to file"""
        if bot_id in self.ai_bots:
            self.ai_bots[bot_id] = bot_config
            self.save_ai_bots()

    def delete_ai_bot(self, bot_id):
        """Delete an AI bot and save to file"""
        if bot_id in self.ai_bots:
            del self.ai_bots[bot_id]
            self.save_ai_bots()

    def get_decimal_places_from_point(self, point_value):
        """Calculate decimal places from symbol point value"""
        if point_value >= 1:
            return 0
        elif point_value >= 0.1:
            return 1
        elif point_value >= 0.01:
            return 2
        elif point_value >= 0.001:
            return 3
        elif point_value >= 0.0001:
            return 4
        elif point_value >= 0.00001:
            return 5
        else:
            return 6  # Default for very small point values

    def get_format_prompt_for_symbol(self, symbol):
        """Generate format prompt with correct decimal places for the given symbol"""
        import MetaTrader5 as mt5

        try:
            # Get symbol info
            symbol_info = mt5.symbol_info(symbol)
            if symbol_info is None:
                # Fallback to 2 decimals if symbol info not available
                decimal_places = 2
            else:
                decimal_places = self.get_decimal_places_from_point(symbol_info.point)

            # Create example prices based on symbol type
            if "XAU" in symbol or "GOLD" in symbol.upper():
                # Gold prices
                example_entry = f"{2045.50:.{decimal_places}f}"
                example_sl = f"{2040.00:.{decimal_places}f}"
                example_tp1 = f"{2050.00:.{decimal_places}f}"
                example_tp2 = f"{2055.00:.{decimal_places}f}"
                example_tp3 = f"{2060.00:.{decimal_places}f}"
                example_tp4 = f"{2065.00:.{decimal_places}f}"
                example_tp5 = f"{2070.00:.{decimal_places}f}"
            elif "JPY" in symbol:
                # JPY pairs typically have different pricing
                example_entry = f"{110.50:.{decimal_places}f}"
                example_sl = f"{109.80:.{decimal_places}f}"
                example_tp1 = f"{111.20:.{decimal_places}f}"
                example_tp2 = f"{111.90:.{decimal_places}f}"
                example_tp3 = f"{112.60:.{decimal_places}f}"
                example_tp4 = f"{113.30:.{decimal_places}f}"
                example_tp5 = f"{114.00:.{decimal_places}f}"
            else:
                # Standard forex pairs
                example_entry = f"{1.0850:.{decimal_places}f}"
                example_sl = f"{1.0800:.{decimal_places}f}"
                example_tp1 = f"{1.0900:.{decimal_places}f}"
                example_tp2 = f"{1.0950:.{decimal_places}f}"
                example_tp3 = f"{1.1000:.{decimal_places}f}"
                example_tp4 = f"{1.1050:.{decimal_places}f}"
                example_tp5 = f"{1.1100:.{decimal_places}f}"

            return f"""
IMPORTANT: You will respond ONLY in this exact format, no additional text:

Signal ID: [8 random characters a-z0-9]
C.GPT
Symbol: [trading symbol]
Signal: [Buy Limit or Sell Limit]
Price: [entry price with {decimal_places} decimal places]
SL: [stop loss price with {decimal_places} decimal places]
TP1: [take profit 1 price with {decimal_places} decimal places]
TP2: [take profit 2 price with {decimal_places} decimal places]
TP3: [take profit 3 price with {decimal_places} decimal places]
TP4: [take profit 4 price with {decimal_places} decimal places]
TP5: [take profit 5 price with {decimal_places} decimal places]
Reason: [50 words max explaining why this signal was generated]
Risk: [50 words max describing risk factors and risk management considerations]

Example:
Signal ID: a7b9c2d4
C.GPT
Symbol: {symbol}
Signal: Buy Limit
Price: {example_entry}
SL: {example_sl}
TP1: {example_tp1}
TP2: {example_tp2}
TP3: {example_tp3}
TP4: {example_tp4}
TP5: {example_tp5}
Reason: RSI oversold at 25, MACD bullish crossover, price bounced off key support. EMA20 trending up, confluence of Fibonacci 61.8% retracement and pivot point support suggests strong buying opportunity with favorable risk-reward ratio.
Risk: Market volatility during US session. Watch for news events that could reverse trend. SL placement below key support minimizes downside. Position size should account for potential gap risk during market open.

Respond with ONLY this format, nothing else. but if no signal at all, respond with "NO SIGNAL" in all caps.
"""
        except Exception as e:
            # Fallback to original format if there's any error
            return self.ai_signal_format["format_prompt"]
