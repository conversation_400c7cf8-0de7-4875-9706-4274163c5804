{"error": false, "message": "AI analysis completed successfully", "symbol": "XAUUSD.iux", "timeframes": ["H1"], "ai_provider": "GPT", "analysis": "Signal ID: p8d4k9s2\nC.GPT\nSymbol: XAUUSD.iux\nSignal: Buy Limit\nPrice: 3446.00\nSL: 3428.00\nTP1: 3452.00\nTP2: 3458.00\nTP3: 3464.00\nTP4: 3470.00\nTP5: 3476.00\nReason: Uptrend on H1 above EMA20 with bullish MACD. RSI high; prefer pullback buy into 3444-3446 demand near prior support, trendline, and H1 pivot. Fibonacci 38.2% aligns. SL below zone; targets retest of highs and extensions.", "prompt": "\nAnalyze the XAUUSD.iux trading data for timeframes ['H1'].\n\nTechnical Analysis Data:\n- Total bars analyzed: 100\n- Timeframes: H1\n- Use Trend Line, Fibonacci Retracement, Support/resistance, Supply/demand and Pivot Points in your analysis.\n\nChart Data Summary:\n\nH1 Timeframe (Latest Bar):\n- Price: O:3452.73 H:3452.8 L:3445.72 C:3448.15\n- EMA20: 3429.28579\n- RSI14: 70.5\n- MACD: 11.31578 Signal: 8.498001\n- RSI25: 68.0 SMA50: 60.8\n- RSI50: 64.1 SMA25: 60.3\n\n\nAdditional Instructions: I bias Buy Limit on XAUUSD, but if the reason for Sell is enough then you cab give Sell Signal, I like using Supply/Demand,  Support/Resistant and Trendline,", "image_analyzed": false, "use_signal_format": 1, "timestamp": "2025-08-30T18:48:45.452959", "signal_data": {"signal_id": "p8d4k9s2", "symbol": "XAUUSD.iux", "signal_type": "Buy Limit", "entry_price": "3446.00", "sl_price": "3428.00", "tp1_price": "3452.00", "tp2_price": "3458.00", "tp3_price": "3464.00", "tp4_price": "3470.00", "tp5_price": "3476.00", "reason": "Uptrend on H1 above EMA20 with bullish MACD. RSI high; prefer pullback buy into 3444-3446 demand near prior support, trendline, and H1 pivot. Fibonacci 38.2% aligns. SL below zone; targets retest of highs and extensions."}, "structured_signal": true}