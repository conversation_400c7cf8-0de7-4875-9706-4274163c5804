2025-09-12 09:16:48,701 [INFO] Serving on http://0.0.0.0:5050
2025-09-12 09:16:49,206 [INFO] \U0001f7e2 Webhook server STARTED on port 5050
2025-09-12 09:16:49,782 [INFO] \U0001f7e2 Orders processing loop started
2025-09-12 09:16:49,803 [DEBUG] Processing auto BE/TP for 1 symbols: {'XAUUSD.iux'}
2025-09-12 09:16:49,803 [INFO] \U0001f7e2 Orders processing loop started
2025-09-12 09:16:49,803 [DEBUG] Processing 3 positions for XAUUSD.iux
2025-09-12 09:16:49,804 [DEBUG] Processing 3 positions for  (SL: True, TP: True)
2025-09-12 09:16:49,851 [DEBUG] DEBUG: Magic number usage - Ticket: 348075168, Magic: 2155, SL Points: 2155, Distance: 21.55000, Symbol: XAUUSD.iux
2025-09-12 09:16:49,852 [INFO] \U0001f4cb TabOrders initialized - Auto BE enabled, auto refresh disabled
2025-09-12 09:16:51,249 [INFO] \U0001f522 #348075168: Magic=2155, SL Points=2155
2025-09-12 09:16:51,249 [DEBUG] #348075168: Entry=3593.70000, Current=3648.02000, SL=3594.20000, TP=3695.20000
2025-09-12 09:16:51,250 [DEBUG] #348075168: point_sl=21.55000, point_be=-3578.29800, is_moving_tp=True
2025-09-12 09:16:51,250 [DEBUG] #348075168: BUY Moving TP - Current: 3648.02000, Trigger: 15.40200
2025-09-12 09:16:51,250 [DEBUG] #348075168: BUY Moving TP factor -166.04631090487237 - New SL: -27.59800, New TP: 59.00200
2025-09-12 09:16:51,251 [DEBUG] #348075168: Modifying order - SL: 3594.20000 -> -27.59800, TP: 3695.20000 -> 59.00200
2025-09-12 09:16:51,271 [ERROR] #348075168 - ERROR in order modification: Order send failed: AutoTrading disabled by client
NoneType: None
2025-09-12 09:16:51,334 [INFO] \u274c #348075168 - ERROR in order modification: Order send failed: AutoTrading disabled by client
2025-09-12 09:16:51,356 [DEBUG] DEBUG: Magic number usage - Ticket: 351201149, Magic: 2800, SL Points: 2800, Distance: 28.00000, Symbol: XAUUSD.iux
2025-09-12 09:16:51,357 [INFO] \U0001f522 #351201149: Magic=2800, SL Points=2800
2025-09-12 09:16:51,357 [DEBUG] #351201149: Entry=3633.00000, Current=3648.02000, SL=3605.00000, TP=3655.00000
2025-09-12 09:16:51,357 [DEBUG] #351201149: point_sl=28.00000, point_be=-3624.45000, is_moving_tp=True
2025-09-12 09:16:51,359 [DEBUG] #351201149: BUY Moving TP - Current: 3648.02000, Trigger: 8.55000
2025-09-12 09:16:51,360 [DEBUG] #351201149: BUY Moving TP factor -129.44464285714287 - New SL: -47.35000, New TP: 65.05000
2025-09-12 09:16:51,360 [DEBUG] #351201149: Modifying order - SL: 3605.00000 -> -47.35000, TP: 3655.00000 -> 65.05000
2025-09-12 09:16:51,380 [ERROR] #351201149 - ERROR in order modification: Order send failed: AutoTrading disabled by client
NoneType: None
2025-09-12 09:16:51,381 [INFO] \u274c #351201149 - ERROR in order modification: Order send failed: AutoTrading disabled by client
2025-09-12 09:16:51,400 [DEBUG] DEBUG: Magic number usage - Ticket: 351201151, Magic: 2800, SL Points: 2800, Distance: 28.00000, Symbol: XAUUSD.iux
2025-09-12 09:16:51,401 [INFO] \U0001f522 #351201151: Magic=2800, SL Points=2800
2025-09-12 09:16:51,402 [DEBUG] #351201151: Entry=3633.00000, Current=3648.02000, SL=3605.00000, TP=3665.00000
2025-09-12 09:16:51,402 [DEBUG] #351201151: point_sl=28.00000, point_be=-3624.35000, is_moving_tp=True
2025-09-12 09:16:51,402 [DEBUG] #351201151: BUY Moving TP - Current: 3648.02000, Trigger: 8.65000
2025-09-12 09:16:51,402 [DEBUG] #351201151: BUY Moving TP factor -129.44107142857143 - New SL: -47.25000, New TP: 65.15000
2025-09-12 09:16:51,403 [DEBUG] #351201151: Modifying order - SL: 3605.00000 -> -47.25000, TP: 3665.00000 -> 65.15000
2025-09-12 09:16:51,421 [ERROR] #351201151 - ERROR in order modification: Order send failed: AutoTrading disabled by client
NoneType: None
2025-09-12 09:16:51,432 [INFO] \u274c #351201151 - ERROR in order modification: Order send failed: AutoTrading disabled by client
2025-09-12 09:16:51,433 [INFO] DEBUG: Order processing - Symbol: XAUUSD.iux, Group: , Total: 3, Processed: 3, Action: SL+TP
2025-09-12 09:16:51,434 [INFO] \U0001f4e6 SL+TP Processing: XAUUSD.iux (All) - 3/3 orders
2025-09-12 09:16:51,434 [DEBUG] Processed 3  positions for SL+TP
2025-09-12 09:17:21,499 [DEBUG] Processing auto BE/TP for 1 symbols: {'XAUUSD.iux'}
2025-09-12 09:17:21,499 [DEBUG] Processing 3 positions for XAUUSD.iux
2025-09-12 09:17:21,500 [DEBUG] Processing 3 positions for  (SL: True, TP: True)
2025-09-12 09:17:21,534 [DEBUG] DEBUG: Magic number usage - Ticket: 348075168, Magic: 2155, SL Points: 2155, Distance: 21.55000, Symbol: XAUUSD.iux
2025-09-12 09:17:21,535 [INFO] \U0001f522 #348075168: Magic=2155, SL Points=2155
2025-09-12 09:17:21,535 [DEBUG] #348075168: Entry=3593.70000, Current=3647.88000, SL=3594.20000, TP=3695.20000
2025-09-12 09:17:21,536 [DEBUG] #348075168: point_sl=21.55000, point_be=-3578.29800, is_moving_tp=True
2025-09-12 09:17:21,536 [DEBUG] #348075168: BUY Moving TP - Current: 3647.88000, Trigger: 15.40200
2025-09-12 09:17:21,536 [DEBUG] #348075168: BUY Moving TP factor -166.04631090487237 - New SL: -27.59800, New TP: 59.00200
2025-09-12 09:17:21,537 [DEBUG] #348075168: Modifying order - SL: 3594.20000 -> -27.59800, TP: 3695.20000 -> 59.00200
2025-09-12 09:17:21,558 [ERROR] #348075168 - ERROR in order modification: Order send failed: AutoTrading disabled by client
NoneType: None
2025-09-12 09:17:21,560 [INFO] \u274c #348075168 - ERROR in order modification: Order send failed: AutoTrading disabled by client
2025-09-12 09:17:21,584 [DEBUG] DEBUG: Magic number usage - Ticket: 351201149, Magic: 2800, SL Points: 2800, Distance: 28.00000, Symbol: XAUUSD.iux
2025-09-12 09:17:21,587 [INFO] \U0001f522 #351201149: Magic=2800, SL Points=2800
2025-09-12 09:17:21,587 [DEBUG] #351201149: Entry=3633.00000, Current=3647.88000, SL=3605.00000, TP=3655.00000
2025-09-12 09:17:21,588 [DEBUG] #351201149: point_sl=28.00000, point_be=-3624.45000, is_moving_tp=True
2025-09-12 09:17:21,588 [DEBUG] #351201149: BUY Moving TP - Current: 3647.88000, Trigger: 8.55000
2025-09-12 09:17:21,588 [DEBUG] #351201149: BUY Moving TP factor -129.44464285714287 - New SL: -47.35000, New TP: 65.05000
2025-09-12 09:17:21,589 [DEBUG] #351201149: Modifying order - SL: 3605.00000 -> -47.35000, TP: 3655.00000 -> 65.05000
2025-09-12 09:17:21,609 [ERROR] #351201149 - ERROR in order modification: Order send failed: AutoTrading disabled by client
NoneType: None
2025-09-12 09:17:21,686 [INFO] \u274c #351201149 - ERROR in order modification: Order send failed: AutoTrading disabled by client
2025-09-12 09:17:21,709 [DEBUG] DEBUG: Magic number usage - Ticket: 351201151, Magic: 2800, SL Points: 2800, Distance: 28.00000, Symbol: XAUUSD.iux
2025-09-12 09:17:21,712 [INFO] \U0001f522 #351201151: Magic=2800, SL Points=2800
2025-09-12 09:17:21,712 [DEBUG] #351201151: Entry=3633.00000, Current=3647.88000, SL=3605.00000, TP=3665.00000
2025-09-12 09:17:21,713 [DEBUG] #351201151: point_sl=28.00000, point_be=-3624.35000, is_moving_tp=True
2025-09-12 09:17:21,713 [DEBUG] #351201151: BUY Moving TP - Current: 3647.88000, Trigger: 8.65000
2025-09-12 09:17:21,713 [DEBUG] #351201151: BUY Moving TP factor -129.44107142857143 - New SL: -47.25000, New TP: 65.15000
2025-09-12 09:17:21,714 [DEBUG] #351201151: Modifying order - SL: 3605.00000 -> -47.25000, TP: 3665.00000 -> 65.15000
2025-09-12 09:17:21,734 [ERROR] #351201151 - ERROR in order modification: Order send failed: AutoTrading disabled by client
NoneType: None
2025-09-12 09:17:21,738 [INFO] \u274c #351201151 - ERROR in order modification: Order send failed: AutoTrading disabled by client
2025-09-12 09:17:21,739 [INFO] DEBUG: Order processing - Symbol: XAUUSD.iux, Group: , Total: 3, Processed: 3, Action: SL+TP
2025-09-12 09:17:21,740 [INFO] \U0001f4e6 SL+TP Processing: XAUUSD.iux (All) - 3/3 orders
2025-09-12 09:17:21,740 [DEBUG] Processed 3  positions for SL+TP
2025-09-12 09:17:51,809 [DEBUG] Processing auto BE/TP for 1 symbols: {'XAUUSD.iux'}
2025-09-12 09:17:51,810 [DEBUG] Processing 3 positions for XAUUSD.iux
2025-09-12 09:17:51,810 [DEBUG] Processing 3 positions for  (SL: True, TP: True)
2025-09-12 09:17:51,851 [DEBUG] DEBUG: Magic number usage - Ticket: 348075168, Magic: 2155, SL Points: 2155, Distance: 21.55000, Symbol: XAUUSD.iux
2025-09-12 09:17:51,852 [INFO] \U0001f522 #348075168: Magic=2155, SL Points=2155
2025-09-12 09:17:51,852 [DEBUG] #348075168: Entry=3593.70000, Current=3647.72000, SL=3594.20000, TP=3695.20000
2025-09-12 09:17:51,853 [DEBUG] #348075168: point_sl=21.55000, point_be=-3578.29800, is_moving_tp=True
2025-09-12 09:17:51,853 [DEBUG] #348075168: BUY Moving TP - Current: 3647.72000, Trigger: 15.40200
2025-09-12 09:17:51,853 [DEBUG] #348075168: BUY Moving TP factor -166.04631090487237 - New SL: -27.59800, New TP: 59.00200
2025-09-12 09:17:51,854 [DEBUG] #348075168: Modifying order - SL: 3594.20000 -> -27.59800, TP: 3695.20000 -> 59.00200
2025-09-12 09:17:51,876 [ERROR] #348075168 - ERROR in order modification: Order send failed: AutoTrading disabled by client
NoneType: None
2025-09-12 09:17:51,886 [INFO] \u274c #348075168 - ERROR in order modification: Order send failed: AutoTrading disabled by client
2025-09-12 09:17:51,906 [DEBUG] DEBUG: Magic number usage - Ticket: 351201149, Magic: 2800, SL Points: 2800, Distance: 28.00000, Symbol: XAUUSD.iux
2025-09-12 09:17:51,907 [INFO] \U0001f522 #351201149: Magic=2800, SL Points=2800
2025-09-12 09:17:51,907 [DEBUG] #351201149: Entry=3633.00000, Current=3647.72000, SL=3605.00000, TP=3655.00000
2025-09-12 09:17:51,908 [DEBUG] #351201149: point_sl=28.00000, point_be=-3624.45000, is_moving_tp=True
2025-09-12 09:17:51,908 [DEBUG] #351201149: BUY Moving TP - Current: 3647.72000, Trigger: 8.55000
2025-09-12 09:17:51,908 [DEBUG] #351201149: BUY Moving TP factor -129.44464285714287 - New SL: -47.35000, New TP: 65.05000
2025-09-12 09:17:51,910 [DEBUG] #351201149: Modifying order - SL: 3605.00000 -> -47.35000, TP: 3655.00000 -> 65.05000
2025-09-12 09:17:51,932 [ERROR] #351201149 - ERROR in order modification: Order send failed: AutoTrading disabled by client
NoneType: None
2025-09-12 09:17:52,018 [INFO] \u274c #351201149 - ERROR in order modification: Order send failed: AutoTrading disabled by client
2025-09-12 09:17:52,037 [DEBUG] DEBUG: Magic number usage - Ticket: 351201151, Magic: 2800, SL Points: 2800, Distance: 28.00000, Symbol: XAUUSD.iux
2025-09-12 09:17:52,038 [INFO] \U0001f522 #351201151: Magic=2800, SL Points=2800
2025-09-12 09:17:52,039 [DEBUG] #351201151: Entry=3633.00000, Current=3647.72000, SL=3605.00000, TP=3665.00000
2025-09-12 09:17:52,039 [DEBUG] #351201151: point_sl=28.00000, point_be=-3624.35000, is_moving_tp=True
2025-09-12 09:17:52,039 [DEBUG] #351201151: BUY Moving TP - Current: 3647.72000, Trigger: 8.65000
2025-09-12 09:17:52,039 [DEBUG] #351201151: BUY Moving TP factor -129.44107142857143 - New SL: -47.25000, New TP: 65.15000
2025-09-12 09:17:52,040 [DEBUG] #351201151: Modifying order - SL: 3605.00000 -> -47.25000, TP: 3665.00000 -> 65.15000
2025-09-12 09:17:52,060 [ERROR] #351201151 - ERROR in order modification: Order send failed: AutoTrading disabled by client
NoneType: None
2025-09-12 09:17:52,065 [INFO] \u274c #351201151 - ERROR in order modification: Order send failed: AutoTrading disabled by client
2025-09-12 09:17:52,065 [INFO] DEBUG: Order processing - Symbol: XAUUSD.iux, Group: , Total: 3, Processed: 3, Action: SL+TP
2025-09-12 09:17:52,067 [INFO] \U0001f4e6 SL+TP Processing: XAUUSD.iux (All) - 3/3 orders
2025-09-12 09:17:52,067 [DEBUG] Processed 3  positions for SL+TP
2025-09-12 09:18:22,136 [DEBUG] Processing auto BE/TP for 1 symbols: {'XAUUSD.iux'}
2025-09-12 09:18:22,137 [DEBUG] Processing 3 positions for XAUUSD.iux
2025-09-12 09:18:22,137 [DEBUG] Processing 3 positions for  (SL: True, TP: True)
2025-09-12 09:18:22,174 [DEBUG] DEBUG: Magic number usage - Ticket: 348075168, Magic: 2155, SL Points: 2155, Distance: 21.55000, Symbol: XAUUSD.iux
2025-09-12 09:18:22,175 [INFO] \U0001f522 #348075168: Magic=2155, SL Points=2155
2025-09-12 09:18:22,176 [DEBUG] #348075168: Entry=3593.70000, Current=3648.22000, SL=3594.20000, TP=3695.20000
2025-09-12 09:18:22,176 [DEBUG] #348075168: point_sl=21.55000, point_be=-3578.29800, is_moving_tp=True
2025-09-12 09:18:22,176 [DEBUG] #348075168: BUY Moving TP - Current: 3648.22000, Trigger: 15.40200
2025-09-12 09:18:22,177 [DEBUG] #348075168: BUY Moving TP factor -166.04631090487237 - New SL: -27.59800, New TP: 59.00200
2025-09-12 09:18:22,177 [DEBUG] #348075168: Modifying order - SL: 3594.20000 -> -27.59800, TP: 3695.20000 -> 59.00200
2025-09-12 09:18:22,201 [ERROR] #348075168 - ERROR in order modification: Order send failed: AutoTrading disabled by client
NoneType: None
2025-09-12 09:18:22,202 [INFO] \u274c #348075168 - ERROR in order modification: Order send failed: AutoTrading disabled by client
2025-09-12 09:18:22,222 [DEBUG] DEBUG: Magic number usage - Ticket: 351201149, Magic: 2800, SL Points: 2800, Distance: 28.00000, Symbol: XAUUSD.iux
2025-09-12 09:18:22,226 [INFO] \U0001f522 #351201149: Magic=2800, SL Points=2800
2025-09-12 09:18:22,226 [DEBUG] #351201149: Entry=3633.00000, Current=3648.22000, SL=3605.00000, TP=3655.00000
2025-09-12 09:18:22,227 [DEBUG] #351201149: point_sl=28.00000, point_be=-3624.45000, is_moving_tp=True
2025-09-12 09:18:22,227 [DEBUG] #351201149: BUY Moving TP - Current: 3648.22000, Trigger: 8.55000
2025-09-12 09:18:22,227 [DEBUG] #351201149: BUY Moving TP factor -129.44464285714287 - New SL: -47.35000, New TP: 65.05000
2025-09-12 09:18:22,227 [DEBUG] #351201149: Modifying order - SL: 3605.00000 -> -47.35000, TP: 3655.00000 -> 65.05000
2025-09-12 09:18:22,247 [ERROR] #351201149 - ERROR in order modification: Order send failed: AutoTrading disabled by client
NoneType: None
2025-09-12 09:18:22,355 [INFO] \u274c #351201149 - ERROR in order modification: Order send failed: AutoTrading disabled by client
2025-09-12 09:18:22,374 [DEBUG] DEBUG: Magic number usage - Ticket: 351201151, Magic: 2800, SL Points: 2800, Distance: 28.00000, Symbol: XAUUSD.iux
2025-09-12 09:18:22,375 [INFO] \U0001f522 #351201151: Magic=2800, SL Points=2800
2025-09-12 09:18:22,376 [DEBUG] #351201151: Entry=3633.00000, Current=3648.22000, SL=3605.00000, TP=3665.00000
2025-09-12 09:18:22,376 [DEBUG] #351201151: point_sl=28.00000, point_be=-3624.35000, is_moving_tp=True
2025-09-12 09:18:22,376 [DEBUG] #351201151: BUY Moving TP - Current: 3648.22000, Trigger: 8.65000
2025-09-12 09:18:22,377 [DEBUG] #351201151: BUY Moving TP factor -129.44107142857143 - New SL: -47.25000, New TP: 65.15000
2025-09-12 09:18:22,377 [DEBUG] #351201151: Modifying order - SL: 3605.00000 -> -47.25000, TP: 3665.00000 -> 65.15000
2025-09-12 09:18:22,396 [ERROR] #351201151 - ERROR in order modification: Order send failed: AutoTrading disabled by client
NoneType: None
2025-09-12 09:18:22,400 [INFO] \u274c #351201151 - ERROR in order modification: Order send failed: AutoTrading disabled by client
2025-09-12 09:18:22,401 [INFO] DEBUG: Order processing - Symbol: XAUUSD.iux, Group: , Total: 3, Processed: 3, Action: SL+TP
2025-09-12 09:18:22,402 [INFO] \U0001f4e6 SL+TP Processing: XAUUSD.iux (All) - 3/3 orders
2025-09-12 09:18:22,402 [DEBUG] Processed 3  positions for SL+TP
2025-09-12 09:18:52,458 [DEBUG] Processing auto BE/TP for 1 symbols: {'XAUUSD.iux'}
2025-09-12 09:18:52,459 [DEBUG] Processing 3 positions for XAUUSD.iux
2025-09-12 09:18:52,459 [DEBUG] Processing 3 positions for  (SL: True, TP: True)
2025-09-12 09:18:52,494 [DEBUG] DEBUG: Magic number usage - Ticket: 348075168, Magic: 2155, SL Points: 2155, Distance: 21.55000, Symbol: XAUUSD.iux
2025-09-12 09:18:52,495 [INFO] \U0001f522 #348075168: Magic=2155, SL Points=2155
2025-09-12 09:18:52,495 [DEBUG] #348075168: Entry=3593.70000, Current=3648.26000, SL=3594.20000, TP=3695.20000
2025-09-12 09:18:52,497 [DEBUG] #348075168: point_sl=21.55000, point_be=-3578.29800, is_moving_tp=True
2025-09-12 09:18:52,497 [DEBUG] #348075168: BUY Moving TP - Current: 3648.26000, Trigger: 15.40200
2025-09-12 09:18:52,497 [DEBUG] #348075168: BUY Moving TP factor -166.04631090487237 - New SL: -27.59800, New TP: 59.00200
2025-09-12 09:18:52,498 [DEBUG] #348075168: Modifying order - SL: 3594.20000 -> -27.59800, TP: 3695.20000 -> 59.00200
2025-09-12 09:18:52,516 [ERROR] #348075168 - ERROR in order modification: Order send failed: AutoTrading disabled by client
NoneType: None
2025-09-12 09:18:52,517 [INFO] \u274c #348075168 - ERROR in order modification: Order send failed: AutoTrading disabled by client
2025-09-12 09:18:52,536 [DEBUG] DEBUG: Magic number usage - Ticket: 351201149, Magic: 2800, SL Points: 2800, Distance: 28.00000, Symbol: XAUUSD.iux
2025-09-12 09:18:52,538 [INFO] \U0001f522 #351201149: Magic=2800, SL Points=2800
2025-09-12 09:18:52,538 [DEBUG] #351201149: Entry=3633.00000, Current=3648.26000, SL=3605.00000, TP=3655.00000
2025-09-12 09:18:52,538 [DEBUG] #351201149: point_sl=28.00000, point_be=-3624.45000, is_moving_tp=True
2025-09-12 09:18:52,540 [DEBUG] #351201149: BUY Moving TP - Current: 3648.26000, Trigger: 8.55000
2025-09-12 09:18:52,540 [DEBUG] #351201149: BUY Moving TP factor -129.44464285714287 - New SL: -47.35000, New TP: 65.05000
2025-09-12 09:18:52,540 [DEBUG] #351201149: Modifying order - SL: 3605.00000 -> -47.35000, TP: 3655.00000 -> 65.05000
2025-09-12 09:18:52,559 [ERROR] #351201149 - ERROR in order modification: Order send failed: AutoTrading disabled by client
NoneType: None
2025-09-12 09:18:52,649 [INFO] \u274c #351201149 - ERROR in order modification: Order send failed: AutoTrading disabled by client
2025-09-12 09:18:52,668 [DEBUG] DEBUG: Magic number usage - Ticket: 351201151, Magic: 2800, SL Points: 2800, Distance: 28.00000, Symbol: XAUUSD.iux
2025-09-12 09:18:52,668 [INFO] \U0001f522 #351201151: Magic=2800, SL Points=2800
2025-09-12 09:18:52,669 [DEBUG] #351201151: Entry=3633.00000, Current=3648.26000, SL=3605.00000, TP=3665.00000
2025-09-12 09:18:52,669 [DEBUG] #351201151: point_sl=28.00000, point_be=-3624.35000, is_moving_tp=True
2025-09-12 09:18:52,669 [DEBUG] #351201151: BUY Moving TP - Current: 3648.26000, Trigger: 8.65000
2025-09-12 09:18:52,670 [DEBUG] #351201151: BUY Moving TP factor -129.44107142857143 - New SL: -47.25000, New TP: 65.15000
2025-09-12 09:18:52,670 [DEBUG] #351201151: Modifying order - SL: 3605.00000 -> -47.25000, TP: 3665.00000 -> 65.15000
2025-09-12 09:18:52,689 [ERROR] #351201151 - ERROR in order modification: Order send failed: AutoTrading disabled by client
NoneType: None
2025-09-12 09:18:52,693 [INFO] \u274c #351201151 - ERROR in order modification: Order send failed: AutoTrading disabled by client
2025-09-12 09:18:52,694 [INFO] DEBUG: Order processing - Symbol: XAUUSD.iux, Group: , Total: 3, Processed: 3, Action: SL+TP
2025-09-12 09:18:52,697 [INFO] \U0001f4e6 SL+TP Processing: XAUUSD.iux (All) - 3/3 orders
2025-09-12 09:18:52,697 [DEBUG] Processed 3  positions for SL+TP
2025-09-12 09:19:22,765 [DEBUG] Processing auto BE/TP for 1 symbols: {'XAUUSD.iux'}
2025-09-12 09:19:22,766 [DEBUG] Processing 3 positions for XAUUSD.iux
2025-09-12 09:19:22,766 [DEBUG] Processing 3 positions for  (SL: True, TP: True)
2025-09-12 09:19:22,803 [DEBUG] DEBUG: Magic number usage - Ticket: 348075168, Magic: 2155, SL Points: 2155, Distance: 21.55000, Symbol: XAUUSD.iux
2025-09-12 09:19:22,803 [INFO] \U0001f522 #348075168: Magic=2155, SL Points=2155
2025-09-12 09:19:22,804 [DEBUG] #348075168: Entry=3593.70000, Current=3648.27000, SL=3594.20000, TP=3695.20000
2025-09-12 09:19:22,804 [DEBUG] #348075168: point_sl=21.55000, point_be=-3578.29800, is_moving_tp=True
2025-09-12 09:19:22,804 [DEBUG] #348075168: BUY Moving TP - Current: 3648.27000, Trigger: 15.40200
2025-09-12 09:19:22,805 [DEBUG] #348075168: BUY Moving TP factor -166.04631090487237 - New SL: -27.59800, New TP: 59.00200
2025-09-12 09:19:22,805 [DEBUG] #348075168: Modifying order - SL: 3594.20000 -> -27.59800, TP: 3695.20000 -> 59.00200
2025-09-12 09:19:22,823 [ERROR] #348075168 - ERROR in order modification: Order send failed: AutoTrading disabled by client
NoneType: None
2025-09-12 09:19:22,824 [INFO] \u274c #348075168 - ERROR in order modification: Order send failed: AutoTrading disabled by client
2025-09-12 09:19:22,843 [DEBUG] DEBUG: Magic number usage - Ticket: 351201149, Magic: 2800, SL Points: 2800, Distance: 28.00000, Symbol: XAUUSD.iux
2025-09-12 09:19:22,845 [INFO] \U0001f522 #351201149: Magic=2800, SL Points=2800
2025-09-12 09:19:22,846 [DEBUG] #351201149: Entry=3633.00000, Current=3648.27000, SL=3605.00000, TP=3655.00000
2025-09-12 09:19:22,846 [DEBUG] #351201149: point_sl=28.00000, point_be=-3624.45000, is_moving_tp=True
2025-09-12 09:19:22,846 [DEBUG] #351201149: BUY Moving TP - Current: 3648.27000, Trigger: 8.55000
2025-09-12 09:19:22,847 [DEBUG] #351201149: BUY Moving TP factor -129.44464285714287 - New SL: -47.35000, New TP: 65.05000
2025-09-12 09:19:22,847 [DEBUG] #351201149: Modifying order - SL: 3605.00000 -> -47.35000, TP: 3655.00000 -> 65.05000
2025-09-12 09:19:22,865 [ERROR] #351201149 - ERROR in order modification: Order send failed: AutoTrading disabled by client
NoneType: None
2025-09-12 09:19:22,962 [INFO] \u274c #351201149 - ERROR in order modification: Order send failed: AutoTrading disabled by client
2025-09-12 09:19:22,979 [DEBUG] DEBUG: Magic number usage - Ticket: 351201151, Magic: 2800, SL Points: 2800, Distance: 28.00000, Symbol: XAUUSD.iux
2025-09-12 09:19:22,980 [INFO] \U0001f522 #351201151: Magic=2800, SL Points=2800
2025-09-12 09:19:22,981 [DEBUG] #351201151: Entry=3633.00000, Current=3648.27000, SL=3605.00000, TP=3665.00000
2025-09-12 09:19:22,981 [DEBUG] #351201151: point_sl=28.00000, point_be=-3624.35000, is_moving_tp=True
2025-09-12 09:19:22,981 [DEBUG] #351201151: BUY Moving TP - Current: 3648.27000, Trigger: 8.65000
2025-09-12 09:19:22,982 [DEBUG] #351201151: BUY Moving TP factor -129.44107142857143 - New SL: -47.25000, New TP: 65.15000
2025-09-12 09:19:22,982 [DEBUG] #351201151: Modifying order - SL: 3605.00000 -> -47.25000, TP: 3665.00000 -> 65.15000
2025-09-12 09:19:23,002 [ERROR] #351201151 - ERROR in order modification: Order send failed: AutoTrading disabled by client
NoneType: None
2025-09-12 09:19:23,007 [INFO] \u274c #351201151 - ERROR in order modification: Order send failed: AutoTrading disabled by client
2025-09-12 09:19:23,008 [INFO] DEBUG: Order processing - Symbol: XAUUSD.iux, Group: , Total: 3, Processed: 3, Action: SL+TP
2025-09-12 09:19:23,009 [INFO] \U0001f4e6 SL+TP Processing: XAUUSD.iux (All) - 3/3 orders
2025-09-12 09:19:23,009 [DEBUG] Processed 3  positions for SL+TP
2025-09-12 09:19:53,065 [DEBUG] Processing auto BE/TP for 1 symbols: {'XAUUSD.iux'}
2025-09-12 09:19:53,066 [DEBUG] Processing 3 positions for XAUUSD.iux
2025-09-12 09:19:53,066 [DEBUG] Processing 3 positions for  (SL: True, TP: True)
2025-09-12 09:19:53,106 [DEBUG] DEBUG: Magic number usage - Ticket: 348075168, Magic: 2155, SL Points: 2155, Distance: 21.55000, Symbol: XAUUSD.iux
2025-09-12 09:19:53,107 [INFO] \U0001f522 #348075168: Magic=2155, SL Points=2155
2025-09-12 09:19:53,108 [DEBUG] #348075168: Entry=3593.70000, Current=3648.55000, SL=3594.20000, TP=3695.20000
2025-09-12 09:19:53,109 [DEBUG] #348075168: point_sl=21.55000, point_be=-3578.29800, is_moving_tp=True
2025-09-12 09:19:53,109 [DEBUG] #348075168: BUY Moving TP - Current: 3648.55000, Trigger: 15.40200
2025-09-12 09:19:53,109 [DEBUG] #348075168: BUY Moving TP factor -166.04631090487237 - New SL: -27.59800, New TP: 59.00200
2025-09-12 09:19:53,110 [DEBUG] #348075168: Modifying order - SL: 3594.20000 -> -27.59800, TP: 3695.20000 -> 59.00200
2025-09-12 09:19:53,134 [ERROR] #348075168 - ERROR in order modification: Order send failed: AutoTrading disabled by client
NoneType: None
2025-09-12 09:19:53,135 [INFO] \u274c #348075168 - ERROR in order modification: Order send failed: AutoTrading disabled by client
2025-09-12 09:19:53,159 [DEBUG] DEBUG: Magic number usage - Ticket: 351201149, Magic: 2800, SL Points: 2800, Distance: 28.00000, Symbol: XAUUSD.iux
2025-09-12 09:19:53,161 [INFO] \U0001f522 #351201149: Magic=2800, SL Points=2800
2025-09-12 09:19:53,161 [DEBUG] #351201149: Entry=3633.00000, Current=3648.55000, SL=3605.00000, TP=3655.00000
2025-09-12 09:19:53,161 [DEBUG] #351201149: point_sl=28.00000, point_be=-3624.45000, is_moving_tp=True
2025-09-12 09:19:53,162 [DEBUG] #351201149: BUY Moving TP - Current: 3648.55000, Trigger: 8.55000
2025-09-12 09:19:53,162 [DEBUG] #351201149: BUY Moving TP factor -129.44464285714287 - New SL: -47.35000, New TP: 65.05000
2025-09-12 09:19:53,162 [DEBUG] #351201149: Modifying order - SL: 3605.00000 -> -47.35000, TP: 3655.00000 -> 65.05000
2025-09-12 09:19:53,183 [ERROR] #351201149 - ERROR in order modification: Order send failed: AutoTrading disabled by client
NoneType: None
2025-09-12 09:19:53,301 [INFO] \u274c #351201149 - ERROR in order modification: Order send failed: AutoTrading disabled by client
2025-09-12 09:19:53,319 [DEBUG] DEBUG: Magic number usage - Ticket: 351201151, Magic: 2800, SL Points: 2800, Distance: 28.00000, Symbol: XAUUSD.iux
2025-09-12 09:19:53,320 [INFO] \U0001f522 #351201151: Magic=2800, SL Points=2800
2025-09-12 09:19:53,321 [DEBUG] #351201151: Entry=3633.00000, Current=3648.55000, SL=3605.00000, TP=3665.00000
2025-09-12 09:19:53,321 [DEBUG] #351201151: point_sl=28.00000, point_be=-3624.35000, is_moving_tp=True
2025-09-12 09:19:53,321 [DEBUG] #351201151: BUY Moving TP - Current: 3648.55000, Trigger: 8.65000
2025-09-12 09:19:53,322 [DEBUG] #351201151: BUY Moving TP factor -129.44107142857143 - New SL: -47.25000, New TP: 65.15000
2025-09-12 09:19:53,322 [DEBUG] #351201151: Modifying order - SL: 3605.00000 -> -47.25000, TP: 3665.00000 -> 65.15000
2025-09-12 09:19:53,342 [ERROR] #351201151 - ERROR in order modification: Order send failed: AutoTrading disabled by client
NoneType: None
2025-09-12 09:19:53,346 [INFO] \u274c #351201151 - ERROR in order modification: Order send failed: AutoTrading disabled by client
2025-09-12 09:19:53,347 [INFO] DEBUG: Order processing - Symbol: XAUUSD.iux, Group: , Total: 3, Processed: 3, Action: SL+TP
2025-09-12 09:19:53,348 [INFO] \U0001f4e6 SL+TP Processing: XAUUSD.iux (All) - 3/3 orders
2025-09-12 09:19:53,349 [DEBUG] Processed 3  positions for SL+TP
2025-09-12 09:20:23,415 [DEBUG] Processing auto BE/TP for 1 symbols: {'XAUUSD.iux'}
2025-09-12 09:20:23,416 [DEBUG] Processing 3 positions for XAUUSD.iux
2025-09-12 09:20:23,416 [DEBUG] Processing 3 positions for  (SL: True, TP: True)
2025-09-12 09:20:23,457 [DEBUG] DEBUG: Magic number usage - Ticket: 348075168, Magic: 2155, SL Points: 2155, Distance: 21.55000, Symbol: XAUUSD.iux
2025-09-12 09:20:23,457 [INFO] \U0001f522 #348075168: Magic=2155, SL Points=2155
2025-09-12 09:20:23,458 [DEBUG] #348075168: Entry=3593.70000, Current=3648.38000, SL=3594.20000, TP=3695.20000
2025-09-12 09:20:23,458 [DEBUG] #348075168: point_sl=21.55000, point_be=-3578.29800, is_moving_tp=True
2025-09-12 09:20:23,458 [DEBUG] #348075168: BUY Moving TP - Current: 3648.38000, Trigger: 15.40200
2025-09-12 09:20:23,459 [DEBUG] #348075168: BUY Moving TP factor -166.04631090487237 - New SL: -27.59800, New TP: 59.00200
2025-09-12 09:20:23,459 [DEBUG] #348075168: Modifying order - SL: 3594.20000 -> -27.59800, TP: 3695.20000 -> 59.00200
2025-09-12 09:20:23,479 [ERROR] #348075168 - ERROR in order modification: Order send failed: AutoTrading disabled by client
NoneType: None
2025-09-12 09:20:23,480 [INFO] \u274c #348075168 - ERROR in order modification: Order send failed: AutoTrading disabled by client
2025-09-12 09:20:23,499 [DEBUG] DEBUG: Magic number usage - Ticket: 351201149, Magic: 2800, SL Points: 2800, Distance: 28.00000, Symbol: XAUUSD.iux
2025-09-12 09:20:23,502 [INFO] \U0001f522 #351201149: Magic=2800, SL Points=2800
2025-09-12 09:20:23,502 [DEBUG] #351201149: Entry=3633.00000, Current=3648.38000, SL=3605.00000, TP=3655.00000
2025-09-12 09:20:23,502 [DEBUG] #351201149: point_sl=28.00000, point_be=-3624.45000, is_moving_tp=True
2025-09-12 09:20:23,503 [DEBUG] #351201149: BUY Moving TP - Current: 3648.38000, Trigger: 8.55000
2025-09-12 09:20:23,503 [DEBUG] #351201149: BUY Moving TP factor -129.44464285714287 - New SL: -47.35000, New TP: 65.05000
2025-09-12 09:20:23,503 [DEBUG] #351201149: Modifying order - SL: 3605.00000 -> -47.35000, TP: 3655.00000 -> 65.05000
2025-09-12 09:20:23,522 [ERROR] #351201149 - ERROR in order modification: Order send failed: AutoTrading disabled by client
NoneType: None
2025-09-12 09:20:23,680 [INFO] \u274c #351201149 - ERROR in order modification: Order send failed: AutoTrading disabled by client
2025-09-12 09:20:23,698 [DEBUG] DEBUG: Magic number usage - Ticket: 351201151, Magic: 2800, SL Points: 2800, Distance: 28.00000, Symbol: XAUUSD.iux
2025-09-12 09:20:23,700 [INFO] \U0001f522 #351201151: Magic=2800, SL Points=2800
2025-09-12 09:20:23,701 [DEBUG] #351201151: Entry=3633.00000, Current=3648.38000, SL=3605.00000, TP=3665.00000
2025-09-12 09:20:23,701 [DEBUG] #351201151: point_sl=28.00000, point_be=-3624.35000, is_moving_tp=True
2025-09-12 09:20:23,701 [DEBUG] #351201151: BUY Moving TP - Current: 3648.38000, Trigger: 8.65000
2025-09-12 09:20:23,702 [DEBUG] #351201151: BUY Moving TP factor -129.44107142857143 - New SL: -47.25000, New TP: 65.15000
2025-09-12 09:20:23,702 [DEBUG] #351201151: Modifying order - SL: 3605.00000 -> -47.25000, TP: 3665.00000 -> 65.15000
2025-09-12 09:20:23,724 [ERROR] #351201151 - ERROR in order modification: Order send failed: AutoTrading disabled by client
NoneType: None
2025-09-12 09:20:23,732 [INFO] \u274c #351201151 - ERROR in order modification: Order send failed: AutoTrading disabled by client
2025-09-12 09:20:23,732 [INFO] DEBUG: Order processing - Symbol: XAUUSD.iux, Group: , Total: 3, Processed: 3, Action: SL+TP
2025-09-12 09:20:23,733 [INFO] \U0001f4e6 SL+TP Processing: XAUUSD.iux (All) - 3/3 orders
2025-09-12 09:20:23,733 [DEBUG] Processed 3  positions for SL+TP
2025-09-12 09:20:53,791 [DEBUG] Processing auto BE/TP for 1 symbols: {'XAUUSD.iux'}
2025-09-12 09:20:53,792 [DEBUG] Processing 3 positions for XAUUSD.iux
2025-09-12 09:20:53,792 [DEBUG] Processing 3 positions for  (SL: True, TP: True)
2025-09-12 09:20:53,834 [DEBUG] DEBUG: Magic number usage - Ticket: 348075168, Magic: 2155, SL Points: 2155, Distance: 21.55000, Symbol: XAUUSD.iux
2025-09-12 09:20:53,836 [INFO] \U0001f522 #348075168: Magic=2155, SL Points=2155
2025-09-12 09:20:53,836 [DEBUG] #348075168: Entry=3593.70000, Current=3648.77000, SL=3594.20000, TP=3695.20000
2025-09-12 09:20:53,836 [DEBUG] #348075168: point_sl=21.55000, point_be=-3578.29800, is_moving_tp=True
2025-09-12 09:20:53,837 [DEBUG] #348075168: BUY Moving TP - Current: 3648.77000, Trigger: 15.40200
2025-09-12 09:20:53,837 [DEBUG] #348075168: BUY Moving TP factor -166.04631090487237 - New SL: -27.59800, New TP: 59.00200
2025-09-12 09:20:53,837 [DEBUG] #348075168: Modifying order - SL: 3594.20000 -> -27.59800, TP: 3695.20000 -> 59.00200
2025-09-12 09:20:53,856 [ERROR] #348075168 - ERROR in order modification: Order send failed: AutoTrading disabled by client
NoneType: None
2025-09-12 09:20:53,857 [INFO] \u274c #348075168 - ERROR in order modification: Order send failed: AutoTrading disabled by client
2025-09-12 09:20:53,878 [DEBUG] DEBUG: Magic number usage - Ticket: 351201149, Magic: 2800, SL Points: 2800, Distance: 28.00000, Symbol: XAUUSD.iux
2025-09-12 09:20:53,881 [INFO] \U0001f522 #351201149: Magic=2800, SL Points=2800
2025-09-12 09:20:53,882 [DEBUG] #351201149: Entry=3633.00000, Current=3648.77000, SL=3605.00000, TP=3655.00000
2025-09-12 09:20:53,882 [DEBUG] #351201149: point_sl=28.00000, point_be=-3624.45000, is_moving_tp=True
2025-09-12 09:20:53,882 [DEBUG] #351201149: BUY Moving TP - Current: 3648.77000, Trigger: 8.55000
2025-09-12 09:20:53,882 [DEBUG] #351201149: BUY Moving TP factor -129.44464285714287 - New SL: -47.35000, New TP: 65.05000
2025-09-12 09:20:53,883 [DEBUG] #351201149: Modifying order - SL: 3605.00000 -> -47.35000, TP: 3655.00000 -> 65.05000
2025-09-12 09:20:53,903 [ERROR] #351201149 - ERROR in order modification: Order send failed: AutoTrading disabled by client
NoneType: None
2025-09-12 09:20:54,047 [INFO] \u274c #351201149 - ERROR in order modification: Order send failed: AutoTrading disabled by client
2025-09-12 09:20:54,067 [DEBUG] DEBUG: Magic number usage - Ticket: 351201151, Magic: 2800, SL Points: 2800, Distance: 28.00000, Symbol: XAUUSD.iux
2025-09-12 09:20:54,068 [INFO] \U0001f522 #351201151: Magic=2800, SL Points=2800
2025-09-12 09:20:54,068 [DEBUG] #351201151: Entry=3633.00000, Current=3648.77000, SL=3605.00000, TP=3665.00000
2025-09-12 09:20:54,068 [DEBUG] #351201151: point_sl=28.00000, point_be=-3624.35000, is_moving_tp=True
2025-09-12 09:20:54,069 [DEBUG] #351201151: BUY Moving TP - Current: 3648.77000, Trigger: 8.65000
2025-09-12 09:20:54,069 [DEBUG] #351201151: BUY Moving TP factor -129.44107142857143 - New SL: -47.25000, New TP: 65.15000
2025-09-12 09:20:54,070 [DEBUG] #351201151: Modifying order - SL: 3605.00000 -> -47.25000, TP: 3665.00000 -> 65.15000
2025-09-12 09:20:54,088 [ERROR] #351201151 - ERROR in order modification: Order send failed: AutoTrading disabled by client
NoneType: None
2025-09-12 09:20:54,094 [INFO] \u274c #351201151 - ERROR in order modification: Order send failed: AutoTrading disabled by client
2025-09-12 09:20:54,094 [INFO] DEBUG: Order processing - Symbol: XAUUSD.iux, Group: , Total: 3, Processed: 3, Action: SL+TP
2025-09-12 09:20:54,096 [INFO] \U0001f4e6 SL+TP Processing: XAUUSD.iux (All) - 3/3 orders
2025-09-12 09:20:54,097 [DEBUG] Processed 3  positions for SL+TP
2025-09-12 09:21:24,155 [DEBUG] Processing auto BE/TP for 1 symbols: {'XAUUSD.iux'}
2025-09-12 09:21:24,155 [DEBUG] Processing 3 positions for XAUUSD.iux
2025-09-12 09:21:24,156 [DEBUG] Processing 3 positions for  (SL: True, TP: True)
2025-09-12 09:21:24,196 [DEBUG] DEBUG: Magic number usage - Ticket: 348075168, Magic: 2155, SL Points: 2155, Distance: 21.55000, Symbol: XAUUSD.iux
2025-09-12 09:21:24,197 [INFO] \U0001f522 #348075168: Magic=2155, SL Points=2155
2025-09-12 09:21:24,197 [DEBUG] #348075168: Entry=3593.70000, Current=3648.89000, SL=3594.20000, TP=3695.20000
2025-09-12 09:21:24,198 [DEBUG] #348075168: point_sl=21.55000, point_be=-3578.29800, is_moving_tp=True
2025-09-12 09:21:24,198 [DEBUG] #348075168: BUY Moving TP - Current: 3648.89000, Trigger: 15.40200
2025-09-12 09:21:24,198 [DEBUG] #348075168: BUY Moving TP factor -166.04631090487237 - New SL: -27.59800, New TP: 59.00200
2025-09-12 09:21:24,199 [DEBUG] #348075168: Modifying order - SL: 3594.20000 -> -27.59800, TP: 3695.20000 -> 59.00200
2025-09-12 09:21:24,218 [ERROR] #348075168 - ERROR in order modification: Order send failed: AutoTrading disabled by client
NoneType: None
2025-09-12 09:21:24,220 [INFO] \u274c #348075168 - ERROR in order modification: Order send failed: AutoTrading disabled by client
2025-09-12 09:21:24,241 [DEBUG] DEBUG: Magic number usage - Ticket: 351201149, Magic: 2800, SL Points: 2800, Distance: 28.00000, Symbol: XAUUSD.iux
2025-09-12 09:21:24,244 [INFO] \U0001f522 #351201149: Magic=2800, SL Points=2800
2025-09-12 09:21:24,244 [DEBUG] #351201149: Entry=3633.00000, Current=3648.89000, SL=3605.00000, TP=3655.00000
2025-09-12 09:21:24,245 [DEBUG] #351201149: point_sl=28.00000, point_be=-3624.45000, is_moving_tp=True
2025-09-12 09:21:24,245 [DEBUG] #351201149: BUY Moving TP - Current: 3648.89000, Trigger: 8.55000
2025-09-12 09:21:24,245 [DEBUG] #351201149: BUY Moving TP factor -129.44464285714287 - New SL: -47.35000, New TP: 65.05000
2025-09-12 09:21:24,246 [DEBUG] #351201149: Modifying order - SL: 3605.00000 -> -47.35000, TP: 3655.00000 -> 65.05000
2025-09-12 09:21:24,266 [ERROR] #351201149 - ERROR in order modification: Order send failed: AutoTrading disabled by client
NoneType: None
2025-09-12 09:21:24,415 [INFO] \u274c #351201149 - ERROR in order modification: Order send failed: AutoTrading disabled by client
2025-09-12 09:21:24,434 [DEBUG] DEBUG: Magic number usage - Ticket: 351201151, Magic: 2800, SL Points: 2800, Distance: 28.00000, Symbol: XAUUSD.iux
2025-09-12 09:21:24,434 [INFO] \U0001f522 #351201151: Magic=2800, SL Points=2800
2025-09-12 09:21:24,435 [DEBUG] #351201151: Entry=3633.00000, Current=3648.89000, SL=3605.00000, TP=3665.00000
2025-09-12 09:21:24,435 [DEBUG] #351201151: point_sl=28.00000, point_be=-3624.35000, is_moving_tp=True
2025-09-12 09:21:24,435 [DEBUG] #351201151: BUY Moving TP - Current: 3648.89000, Trigger: 8.65000
2025-09-12 09:21:24,436 [DEBUG] #351201151: BUY Moving TP factor -129.44107142857143 - New SL: -47.25000, New TP: 65.15000
2025-09-12 09:21:24,436 [DEBUG] #351201151: Modifying order - SL: 3605.00000 -> -47.25000, TP: 3665.00000 -> 65.15000
2025-09-12 09:21:24,456 [ERROR] #351201151 - ERROR in order modification: Order send failed: AutoTrading disabled by client
NoneType: None
2025-09-12 09:21:24,462 [INFO] \u274c #351201151 - ERROR in order modification: Order send failed: AutoTrading disabled by client
2025-09-12 09:21:24,462 [INFO] DEBUG: Order processing - Symbol: XAUUSD.iux, Group: , Total: 3, Processed: 3, Action: SL+TP
2025-09-12 09:21:24,464 [INFO] \U0001f4e6 SL+TP Processing: XAUUSD.iux (All) - 3/3 orders
2025-09-12 09:21:24,465 [DEBUG] Processed 3  positions for SL+TP
2025-09-12 09:21:54,526 [DEBUG] Processing auto BE/TP for 1 symbols: {'XAUUSD.iux'}
2025-09-12 09:21:54,526 [DEBUG] Processing 3 positions for XAUUSD.iux
2025-09-12 09:21:54,527 [DEBUG] Processing 3 positions for  (SL: True, TP: True)
2025-09-12 09:21:54,565 [DEBUG] DEBUG: Magic number usage - Ticket: 348075168, Magic: 2155, SL Points: 2155, Distance: 21.55000, Symbol: XAUUSD.iux
2025-09-12 09:21:54,565 [INFO] \U0001f522 #348075168: Magic=2155, SL Points=2155
2025-09-12 09:21:54,566 [DEBUG] #348075168: Entry=3593.70000, Current=3649.03000, SL=3594.20000, TP=3695.20000
2025-09-12 09:21:54,566 [DEBUG] #348075168: point_sl=21.55000, point_be=-3578.29800, is_moving_tp=True
2025-09-12 09:21:54,567 [DEBUG] #348075168: BUY Moving TP - Current: 3649.03000, Trigger: 15.40200
2025-09-12 09:21:54,567 [DEBUG] #348075168: BUY Moving TP factor -166.04631090487237 - New SL: -27.59800, New TP: 59.00200
2025-09-12 09:21:54,567 [DEBUG] #348075168: Modifying order - SL: 3594.20000 -> -27.59800, TP: 3695.20000 -> 59.00200
2025-09-12 09:21:54,587 [ERROR] #348075168 - ERROR in order modification: Order send failed: AutoTrading disabled by client
NoneType: None
2025-09-12 09:21:54,588 [INFO] \u274c #348075168 - ERROR in order modification: Order send failed: AutoTrading disabled by client
2025-09-12 09:21:54,608 [DEBUG] DEBUG: Magic number usage - Ticket: 351201149, Magic: 2800, SL Points: 2800, Distance: 28.00000, Symbol: XAUUSD.iux
2025-09-12 09:21:54,610 [INFO] \U0001f522 #351201149: Magic=2800, SL Points=2800
2025-09-12 09:21:54,610 [DEBUG] #351201149: Entry=3633.00000, Current=3649.03000, SL=3605.00000, TP=3655.00000
2025-09-12 09:21:54,611 [DEBUG] #351201149: point_sl=28.00000, point_be=-3624.45000, is_moving_tp=True
2025-09-12 09:21:54,611 [DEBUG] #351201149: BUY Moving TP - Current: 3649.03000, Trigger: 8.55000
2025-09-12 09:21:54,611 [DEBUG] #351201149: BUY Moving TP factor -129.44464285714287 - New SL: -47.35000, New TP: 65.05000
2025-09-12 09:21:54,611 [DEBUG] #351201149: Modifying order - SL: 3605.00000 -> -47.35000, TP: 3655.00000 -> 65.05000
2025-09-12 09:21:54,632 [ERROR] #351201149 - ERROR in order modification: Order send failed: AutoTrading disabled by client
NoneType: None
2025-09-12 09:21:54,809 [INFO] \u274c #351201149 - ERROR in order modification: Order send failed: AutoTrading disabled by client
2025-09-12 09:21:54,828 [DEBUG] DEBUG: Magic number usage - Ticket: 351201151, Magic: 2800, SL Points: 2800, Distance: 28.00000, Symbol: XAUUSD.iux
2025-09-12 09:21:54,829 [INFO] \U0001f522 #351201151: Magic=2800, SL Points=2800
2025-09-12 09:21:54,829 [DEBUG] #351201151: Entry=3633.00000, Current=3649.03000, SL=3605.00000, TP=3665.00000
2025-09-12 09:21:54,829 [DEBUG] #351201151: point_sl=28.00000, point_be=-3624.35000, is_moving_tp=True
2025-09-12 09:21:54,829 [DEBUG] #351201151: BUY Moving TP - Current: 3649.03000, Trigger: 8.65000
2025-09-12 09:21:54,830 [DEBUG] #351201151: BUY Moving TP factor -129.44107142857143 - New SL: -47.25000, New TP: 65.15000
2025-09-12 09:21:54,830 [DEBUG] #351201151: Modifying order - SL: 3605.00000 -> -47.25000, TP: 3665.00000 -> 65.15000
2025-09-12 09:21:54,850 [ERROR] #351201151 - ERROR in order modification: Order send failed: AutoTrading disabled by client
NoneType: None
2025-09-12 09:21:54,854 [INFO] \u274c #351201151 - ERROR in order modification: Order send failed: AutoTrading disabled by client
2025-09-12 09:21:54,854 [INFO] DEBUG: Order processing - Symbol: XAUUSD.iux, Group: , Total: 3, Processed: 3, Action: SL+TP
2025-09-12 09:21:54,856 [INFO] \U0001f4e6 SL+TP Processing: XAUUSD.iux (All) - 3/3 orders
2025-09-12 09:21:54,857 [DEBUG] Processed 3  positions for SL+TP
2025-09-12 09:22:24,920 [DEBUG] Processing auto BE/TP for 1 symbols: {'XAUUSD.iux'}
2025-09-12 09:22:24,920 [DEBUG] Processing 3 positions for XAUUSD.iux
2025-09-12 09:22:24,921 [DEBUG] Processing 3 positions for  (SL: True, TP: True)
2025-09-12 09:22:24,963 [DEBUG] DEBUG: Magic number usage - Ticket: 348075168, Magic: 2155, SL Points: 2155, Distance: 21.55000, Symbol: XAUUSD.iux
2025-09-12 09:22:24,963 [INFO] \U0001f522 #348075168: Magic=2155, SL Points=2155
2025-09-12 09:22:24,964 [DEBUG] #348075168: Entry=3593.70000, Current=3649.14000, SL=3594.20000, TP=3695.20000
2025-09-12 09:22:24,965 [DEBUG] #348075168: point_sl=21.55000, point_be=-3578.29800, is_moving_tp=True
2025-09-12 09:22:24,965 [DEBUG] #348075168: BUY Moving TP - Current: 3649.14000, Trigger: 15.40200
2025-09-12 09:22:24,965 [DEBUG] #348075168: BUY Moving TP factor -166.04631090487237 - New SL: -27.59800, New TP: 59.00200
2025-09-12 09:22:24,966 [DEBUG] #348075168: Modifying order - SL: 3594.20000 -> -27.59800, TP: 3695.20000 -> 59.00200
2025-09-12 09:22:24,987 [ERROR] #348075168 - ERROR in order modification: Order send failed: AutoTrading disabled by client
NoneType: None
2025-09-12 09:22:24,988 [INFO] \u274c #348075168 - ERROR in order modification: Order send failed: AutoTrading disabled by client
2025-09-12 09:22:25,009 [DEBUG] DEBUG: Magic number usage - Ticket: 351201149, Magic: 2800, SL Points: 2800, Distance: 28.00000, Symbol: XAUUSD.iux
2025-09-12 09:22:25,013 [INFO] \U0001f522 #351201149: Magic=2800, SL Points=2800
2025-09-12 09:22:25,013 [DEBUG] #351201149: Entry=3633.00000, Current=3649.14000, SL=3605.00000, TP=3655.00000
2025-09-12 09:22:25,013 [DEBUG] #351201149: point_sl=28.00000, point_be=-3624.45000, is_moving_tp=True
2025-09-12 09:22:25,015 [DEBUG] #351201149: BUY Moving TP - Current: 3649.14000, Trigger: 8.55000
2025-09-12 09:22:25,015 [DEBUG] #351201149: BUY Moving TP factor -129.44464285714287 - New SL: -47.35000, New TP: 65.05000
2025-09-12 09:22:25,015 [DEBUG] #351201149: Modifying order - SL: 3605.00000 -> -47.35000, TP: 3655.00000 -> 65.05000
2025-09-12 09:22:25,036 [ERROR] #351201149 - ERROR in order modification: Order send failed: AutoTrading disabled by client
NoneType: None
2025-09-12 09:22:25,224 [INFO] \u274c #351201149 - ERROR in order modification: Order send failed: AutoTrading disabled by client
2025-09-12 09:22:25,243 [DEBUG] DEBUG: Magic number usage - Ticket: 351201151, Magic: 2800, SL Points: 2800, Distance: 28.00000, Symbol: XAUUSD.iux
2025-09-12 09:22:25,244 [INFO] \U0001f522 #351201151: Magic=2800, SL Points=2800
2025-09-12 09:22:25,244 [DEBUG] #351201151: Entry=3633.00000, Current=3649.14000, SL=3605.00000, TP=3665.00000
2025-09-12 09:22:25,244 [DEBUG] #351201151: point_sl=28.00000, point_be=-3624.35000, is_moving_tp=True
2025-09-12 09:22:25,245 [DEBUG] #351201151: BUY Moving TP - Current: 3649.14000, Trigger: 8.65000
2025-09-12 09:22:25,245 [DEBUG] #351201151: BUY Moving TP factor -129.44107142857143 - New SL: -47.25000, New TP: 65.15000
2025-09-12 09:22:25,245 [DEBUG] #351201151: Modifying order - SL: 3605.00000 -> -47.25000, TP: 3665.00000 -> 65.15000
2025-09-12 09:22:25,264 [ERROR] #351201151 - ERROR in order modification: Order send failed: AutoTrading disabled by client
NoneType: None
2025-09-12 09:22:25,269 [INFO] \u274c #351201151 - ERROR in order modification: Order send failed: AutoTrading disabled by client
2025-09-12 09:22:25,269 [INFO] DEBUG: Order processing - Symbol: XAUUSD.iux, Group: , Total: 3, Processed: 3, Action: SL+TP
2025-09-12 09:22:25,270 [INFO] \U0001f4e6 SL+TP Processing: XAUUSD.iux (All) - 3/3 orders
2025-09-12 09:22:25,271 [DEBUG] Processed 3  positions for SL+TP
2025-09-12 09:22:55,326 [DEBUG] Processing auto BE/TP for 1 symbols: {'XAUUSD.iux'}
2025-09-12 09:22:55,327 [DEBUG] Processing 3 positions for XAUUSD.iux
2025-09-12 09:22:55,327 [DEBUG] Processing 3 positions for  (SL: True, TP: True)
2025-09-12 09:22:55,367 [DEBUG] DEBUG: Magic number usage - Ticket: 348075168, Magic: 2155, SL Points: 2155, Distance: 21.55000, Symbol: XAUUSD.iux
2025-09-12 09:22:55,367 [INFO] \U0001f522 #348075168: Magic=2155, SL Points=2155
2025-09-12 09:22:55,368 [DEBUG] #348075168: Entry=3593.70000, Current=3648.79000, SL=3594.20000, TP=3695.20000
2025-09-12 09:22:55,368 [DEBUG] #348075168: point_sl=21.55000, point_be=-3578.29800, is_moving_tp=True
2025-09-12 09:22:55,369 [DEBUG] #348075168: BUY Moving TP - Current: 3648.79000, Trigger: 15.40200
2025-09-12 09:22:55,370 [DEBUG] #348075168: BUY Moving TP factor -166.04631090487237 - New SL: -27.59800, New TP: 59.00200
2025-09-12 09:22:55,370 [DEBUG] #348075168: Modifying order - SL: 3594.20000 -> -27.59800, TP: 3695.20000 -> 59.00200
2025-09-12 09:22:55,389 [ERROR] #348075168 - ERROR in order modification: Order send failed: AutoTrading disabled by client
NoneType: None
2025-09-12 09:22:55,390 [INFO] \u274c #348075168 - ERROR in order modification: Order send failed: AutoTrading disabled by client
2025-09-12 09:22:55,409 [DEBUG] DEBUG: Magic number usage - Ticket: 351201149, Magic: 2800, SL Points: 2800, Distance: 28.00000, Symbol: XAUUSD.iux
2025-09-12 09:22:55,411 [INFO] \U0001f522 #351201149: Magic=2800, SL Points=2800
2025-09-12 09:22:55,412 [DEBUG] #351201149: Entry=3633.00000, Current=3648.79000, SL=3605.00000, TP=3655.00000
2025-09-12 09:22:55,412 [DEBUG] #351201149: point_sl=28.00000, point_be=-3624.45000, is_moving_tp=True
2025-09-12 09:22:55,412 [DEBUG] #351201149: BUY Moving TP - Current: 3648.79000, Trigger: 8.55000
2025-09-12 09:22:55,413 [DEBUG] #351201149: BUY Moving TP factor -129.44464285714287 - New SL: -47.35000, New TP: 65.05000
2025-09-12 09:22:55,413 [DEBUG] #351201149: Modifying order - SL: 3605.00000 -> -47.35000, TP: 3655.00000 -> 65.05000
2025-09-12 09:22:55,433 [ERROR] #351201149 - ERROR in order modification: Order send failed: AutoTrading disabled by client
NoneType: None
2025-09-12 09:22:55,616 [INFO] \u274c #351201149 - ERROR in order modification: Order send failed: AutoTrading disabled by client
2025-09-12 09:22:55,636 [DEBUG] DEBUG: Magic number usage - Ticket: 351201151, Magic: 2800, SL Points: 2800, Distance: 28.00000, Symbol: XAUUSD.iux
2025-09-12 09:22:55,636 [INFO] \U0001f522 #351201151: Magic=2800, SL Points=2800
2025-09-12 09:22:55,636 [DEBUG] #351201151: Entry=3633.00000, Current=3648.79000, SL=3605.00000, TP=3665.00000
2025-09-12 09:22:55,637 [DEBUG] #351201151: point_sl=28.00000, point_be=-3624.35000, is_moving_tp=True
2025-09-12 09:22:55,637 [DEBUG] #351201151: BUY Moving TP - Current: 3648.79000, Trigger: 8.65000
2025-09-12 09:22:55,637 [DEBUG] #351201151: BUY Moving TP factor -129.44107142857143 - New SL: -47.25000, New TP: 65.15000
2025-09-12 09:22:55,638 [DEBUG] #351201151: Modifying order - SL: 3605.00000 -> -47.25000, TP: 3665.00000 -> 65.15000
2025-09-12 09:22:55,656 [ERROR] #351201151 - ERROR in order modification: Order send failed: AutoTrading disabled by client
NoneType: None
2025-09-12 09:22:55,662 [INFO] \u274c #351201151 - ERROR in order modification: Order send failed: AutoTrading disabled by client
2025-09-12 09:22:55,663 [INFO] DEBUG: Order processing - Symbol: XAUUSD.iux, Group: , Total: 3, Processed: 3, Action: SL+TP
2025-09-12 09:22:55,664 [INFO] \U0001f4e6 SL+TP Processing: XAUUSD.iux (All) - 3/3 orders
2025-09-12 09:22:55,664 [DEBUG] Processed 3  positions for SL+TP
2025-09-12 09:23:25,729 [DEBUG] Processing auto BE/TP for 1 symbols: {'XAUUSD.iux'}
2025-09-12 09:23:25,729 [DEBUG] Processing 3 positions for XAUUSD.iux
2025-09-12 09:23:25,729 [DEBUG] Processing 3 positions for  (SL: True, TP: True)
2025-09-12 09:23:25,770 [DEBUG] DEBUG: Magic number usage - Ticket: 348075168, Magic: 2155, SL Points: 2155, Distance: 21.55000, Symbol: XAUUSD.iux
2025-09-12 09:23:25,771 [INFO] \U0001f522 #348075168: Magic=2155, SL Points=2155
2025-09-12 09:23:25,773 [DEBUG] #348075168: Entry=3593.70000, Current=3648.69000, SL=3594.20000, TP=3695.20000
2025-09-12 09:23:25,774 [DEBUG] #348075168: point_sl=21.55000, point_be=-3578.29800, is_moving_tp=True
2025-09-12 09:23:25,774 [DEBUG] #348075168: BUY Moving TP - Current: 3648.69000, Trigger: 15.40200
2025-09-12 09:23:25,774 [DEBUG] #348075168: BUY Moving TP factor -166.04631090487237 - New SL: -27.59800, New TP: 59.00200
2025-09-12 09:23:25,775 [DEBUG] #348075168: Modifying order - SL: 3594.20000 -> -27.59800, TP: 3695.20000 -> 59.00200
2025-09-12 09:23:25,793 [ERROR] #348075168 - ERROR in order modification: Order send failed: AutoTrading disabled by client
NoneType: None
2025-09-12 09:23:25,794 [INFO] \u274c #348075168 - ERROR in order modification: Order send failed: AutoTrading disabled by client
2025-09-12 09:23:25,813 [DEBUG] DEBUG: Magic number usage - Ticket: 351201149, Magic: 2800, SL Points: 2800, Distance: 28.00000, Symbol: XAUUSD.iux
2025-09-12 09:23:25,816 [INFO] \U0001f522 #351201149: Magic=2800, SL Points=2800
2025-09-12 09:23:25,816 [DEBUG] #351201149: Entry=3633.00000, Current=3648.69000, SL=3605.00000, TP=3655.00000
2025-09-12 09:23:25,817 [DEBUG] #351201149: point_sl=28.00000, point_be=-3624.45000, is_moving_tp=True
2025-09-12 09:23:25,817 [DEBUG] #351201149: BUY Moving TP - Current: 3648.69000, Trigger: 8.55000
2025-09-12 09:23:25,817 [DEBUG] #351201149: BUY Moving TP factor -129.44464285714287 - New SL: -47.35000, New TP: 65.05000
2025-09-12 09:23:25,818 [DEBUG] #351201149: Modifying order - SL: 3605.00000 -> -47.35000, TP: 3655.00000 -> 65.05000
2025-09-12 09:23:25,837 [ERROR] #351201149 - ERROR in order modification: Order send failed: AutoTrading disabled by client
NoneType: None
2025-09-12 09:23:26,050 [INFO] \u274c #351201149 - ERROR in order modification: Order send failed: AutoTrading disabled by client
2025-09-12 09:23:26,070 [DEBUG] DEBUG: Magic number usage - Ticket: 351201151, Magic: 2800, SL Points: 2800, Distance: 28.00000, Symbol: XAUUSD.iux
2025-09-12 09:23:26,071 [INFO] \U0001f522 #351201151: Magic=2800, SL Points=2800
2025-09-12 09:23:26,071 [DEBUG] #351201151: Entry=3633.00000, Current=3648.69000, SL=3605.00000, TP=3665.00000
2025-09-12 09:23:26,072 [DEBUG] #351201151: point_sl=28.00000, point_be=-3624.35000, is_moving_tp=True
2025-09-12 09:23:26,072 [DEBUG] #351201151: BUY Moving TP - Current: 3648.69000, Trigger: 8.65000
2025-09-12 09:23:26,072 [DEBUG] #351201151: BUY Moving TP factor -129.44107142857143 - New SL: -47.25000, New TP: 65.15000
2025-09-12 09:23:26,073 [DEBUG] #351201151: Modifying order - SL: 3605.00000 -> -47.25000, TP: 3665.00000 -> 65.15000
2025-09-12 09:23:26,092 [ERROR] #351201151 - ERROR in order modification: Order send failed: AutoTrading disabled by client
NoneType: None
2025-09-12 09:23:26,096 [INFO] \u274c #351201151 - ERROR in order modification: Order send failed: AutoTrading disabled by client
2025-09-12 09:23:26,097 [INFO] DEBUG: Order processing - Symbol: XAUUSD.iux, Group: , Total: 3, Processed: 3, Action: SL+TP
2025-09-12 09:23:26,098 [INFO] \U0001f4e6 SL+TP Processing: XAUUSD.iux (All) - 3/3 orders
2025-09-12 09:23:26,098 [DEBUG] Processed 3  positions for SL+TP
2025-09-12 09:23:56,156 [DEBUG] Processing auto BE/TP for 1 symbols: {'XAUUSD.iux'}
2025-09-12 09:23:56,157 [DEBUG] Processing 3 positions for XAUUSD.iux
2025-09-12 09:23:56,157 [DEBUG] Processing 3 positions for  (SL: True, TP: True)
2025-09-12 09:23:56,196 [DEBUG] DEBUG: Magic number usage - Ticket: 348075168, Magic: 2155, SL Points: 2155, Distance: 21.55000, Symbol: XAUUSD.iux
2025-09-12 09:23:56,196 [INFO] \U0001f522 #348075168: Magic=2155, SL Points=2155
2025-09-12 09:23:56,197 [DEBUG] #348075168: Entry=3593.70000, Current=3649.62000, SL=3594.20000, TP=3695.20000
2025-09-12 09:23:56,197 [DEBUG] #348075168: point_sl=21.55000, point_be=-3578.29800, is_moving_tp=True
2025-09-12 09:23:56,198 [DEBUG] #348075168: BUY Moving TP - Current: 3649.62000, Trigger: 15.40200
2025-09-12 09:23:56,198 [DEBUG] #348075168: BUY Moving TP factor -166.04631090487237 - New SL: -27.59800, New TP: 59.00200
2025-09-12 09:23:56,198 [DEBUG] #348075168: Modifying order - SL: 3594.20000 -> -27.59800, TP: 3695.20000 -> 59.00200
2025-09-12 09:23:56,219 [ERROR] #348075168 - ERROR in order modification: Order send failed: AutoTrading disabled by client
NoneType: None
2025-09-12 09:23:56,223 [INFO] \u274c #348075168 - ERROR in order modification: Order send failed: AutoTrading disabled by client
2025-09-12 09:23:56,243 [DEBUG] DEBUG: Magic number usage - Ticket: 351201149, Magic: 2800, SL Points: 2800, Distance: 28.00000, Symbol: XAUUSD.iux
2025-09-12 09:23:56,348 [INFO] \U0001f522 #351201149: Magic=2800, SL Points=2800
2025-09-12 09:23:56,348 [DEBUG] #351201149: Entry=3633.00000, Current=3649.62000, SL=3605.00000, TP=3655.00000
2025-09-12 09:23:56,349 [DEBUG] #351201149: point_sl=28.00000, point_be=-3624.45000, is_moving_tp=True
2025-09-12 09:23:56,349 [DEBUG] #351201149: BUY Moving TP - Current: 3649.62000, Trigger: 8.55000
2025-09-12 09:23:56,349 [DEBUG] #351201149: BUY Moving TP factor -129.44464285714287 - New SL: -47.35000, New TP: 65.05000
2025-09-12 09:23:56,350 [DEBUG] #351201149: Modifying order - SL: 3605.00000 -> -47.35000, TP: 3655.00000 -> 65.05000
2025-09-12 09:23:56,369 [ERROR] #351201149 - ERROR in order modification: Order send failed: AutoTrading disabled by client
NoneType: None
2025-09-12 09:23:56,481 [INFO] \u274c #351201149 - ERROR in order modification: Order send failed: AutoTrading disabled by client
2025-09-12 09:23:56,500 [DEBUG] DEBUG: Magic number usage - Ticket: 351201151, Magic: 2800, SL Points: 2800, Distance: 28.00000, Symbol: XAUUSD.iux
2025-09-12 09:23:56,502 [INFO] \U0001f522 #351201151: Magic=2800, SL Points=2800
2025-09-12 09:23:56,502 [DEBUG] #351201151: Entry=3633.00000, Current=3649.62000, SL=3605.00000, TP=3665.00000
2025-09-12 09:23:56,502 [DEBUG] #351201151: point_sl=28.00000, point_be=-3624.35000, is_moving_tp=True
2025-09-12 09:23:56,503 [DEBUG] #351201151: BUY Moving TP - Current: 3649.62000, Trigger: 8.65000
2025-09-12 09:23:56,503 [DEBUG] #351201151: BUY Moving TP factor -129.44107142857143 - New SL: -47.25000, New TP: 65.15000
2025-09-12 09:23:56,503 [DEBUG] #351201151: Modifying order - SL: 3605.00000 -> -47.25000, TP: 3665.00000 -> 65.15000
2025-09-12 09:23:56,523 [ERROR] #351201151 - ERROR in order modification: Order send failed: AutoTrading disabled by client
NoneType: None
2025-09-12 09:23:56,641 [INFO] \u274c #351201151 - ERROR in order modification: Order send failed: AutoTrading disabled by client
2025-09-12 09:23:56,642 [INFO] DEBUG: Order processing - Symbol: XAUUSD.iux, Group: , Total: 3, Processed: 3, Action: SL+TP
2025-09-12 09:23:56,643 [INFO] \U0001f4e6 SL+TP Processing: XAUUSD.iux (All) - 3/3 orders
2025-09-12 09:23:56,643 [DEBUG] Processed 3  positions for SL+TP
2025-09-12 09:24:26,704 [DEBUG] Processing auto BE/TP for 1 symbols: {'XAUUSD.iux'}
2025-09-12 09:24:26,704 [DEBUG] Processing 3 positions for XAUUSD.iux
2025-09-12 09:24:26,705 [DEBUG] Processing 3 positions for  (SL: True, TP: True)
2025-09-12 09:24:26,743 [DEBUG] DEBUG: Magic number usage - Ticket: 348075168, Magic: 2155, SL Points: 2155, Distance: 21.55000, Symbol: XAUUSD.iux
2025-09-12 09:24:26,743 [INFO] \U0001f522 #348075168: Magic=2155, SL Points=2155
2025-09-12 09:24:26,744 [DEBUG] #348075168: Entry=3593.70000, Current=3649.68000, SL=3594.20000, TP=3695.20000
2025-09-12 09:24:26,744 [DEBUG] #348075168: point_sl=21.55000, point_be=-3578.29800, is_moving_tp=True
2025-09-12 09:24:26,744 [DEBUG] #348075168: BUY Moving TP - Current: 3649.68000, Trigger: 15.40200
2025-09-12 09:24:26,745 [DEBUG] #348075168: BUY Moving TP factor -166.04631090487237 - New SL: -27.59800, New TP: 59.00200
2025-09-12 09:24:26,745 [DEBUG] #348075168: Modifying order - SL: 3594.20000 -> -27.59800, TP: 3695.20000 -> 59.00200
2025-09-12 09:24:26,764 [ERROR] #348075168 - ERROR in order modification: Order send failed: AutoTrading disabled by client
NoneType: None
2025-09-12 09:24:26,860 [INFO] \u274c #348075168 - ERROR in order modification: Order send failed: AutoTrading disabled by client
2025-09-12 09:24:26,879 [DEBUG] DEBUG: Magic number usage - Ticket: 351201149, Magic: 2800, SL Points: 2800, Distance: 28.00000, Symbol: XAUUSD.iux
2025-09-12 09:24:26,880 [INFO] \U0001f522 #351201149: Magic=2800, SL Points=2800
2025-09-12 09:24:26,881 [DEBUG] #351201149: Entry=3633.00000, Current=3649.68000, SL=3605.00000, TP=3655.00000
2025-09-12 09:24:26,881 [DEBUG] #351201149: point_sl=28.00000, point_be=-3624.45000, is_moving_tp=True
2025-09-12 09:24:26,881 [DEBUG] #351201149: BUY Moving TP - Current: 3649.68000, Trigger: 8.55000
2025-09-12 09:24:26,882 [DEBUG] #351201149: BUY Moving TP factor -129.44464285714287 - New SL: -47.35000, New TP: 65.05000
2025-09-12 09:24:26,882 [DEBUG] #351201149: Modifying order - SL: 3605.00000 -> -47.35000, TP: 3655.00000 -> 65.05000
2025-09-12 09:24:26,901 [ERROR] #351201149 - ERROR in order modification: Order send failed: AutoTrading disabled by client
NoneType: None
2025-09-12 09:24:26,922 [INFO] \u274c #351201149 - ERROR in order modification: Order send failed: AutoTrading disabled by client
2025-09-12 09:24:26,941 [DEBUG] DEBUG: Magic number usage - Ticket: 351201151, Magic: 2800, SL Points: 2800, Distance: 28.00000, Symbol: XAUUSD.iux
2025-09-12 09:24:26,942 [INFO] \U0001f522 #351201151: Magic=2800, SL Points=2800
2025-09-12 09:24:26,942 [DEBUG] #351201151: Entry=3633.00000, Current=3649.68000, SL=3605.00000, TP=3665.00000
2025-09-12 09:24:26,943 [DEBUG] #351201151: point_sl=28.00000, point_be=-3624.35000, is_moving_tp=True
2025-09-12 09:24:26,943 [DEBUG] #351201151: BUY Moving TP - Current: 3649.68000, Trigger: 8.65000
2025-09-12 09:24:26,943 [DEBUG] #351201151: BUY Moving TP factor -129.44107142857143 - New SL: -47.25000, New TP: 65.15000
2025-09-12 09:24:26,944 [DEBUG] #351201151: Modifying order - SL: 3605.00000 -> -47.25000, TP: 3665.00000 -> 65.15000
2025-09-12 09:24:26,962 [ERROR] #351201151 - ERROR in order modification: Order send failed: AutoTrading disabled by client
NoneType: None
2025-09-12 09:24:27,060 [INFO] \u274c #351201151 - ERROR in order modification: Order send failed: AutoTrading disabled by client
2025-09-12 09:24:27,060 [INFO] DEBUG: Order processing - Symbol: XAUUSD.iux, Group: , Total: 3, Processed: 3, Action: SL+TP
2025-09-12 09:24:27,062 [INFO] \U0001f4e6 SL+TP Processing: XAUUSD.iux (All) - 3/3 orders
2025-09-12 09:24:27,062 [DEBUG] Processed 3  positions for SL+TP
2025-09-12 09:24:57,115 [DEBUG] Processing auto BE/TP for 1 symbols: {'XAUUSD.iux'}
2025-09-12 09:24:57,116 [DEBUG] Processing 3 positions for XAUUSD.iux
2025-09-12 09:24:57,116 [DEBUG] Processing 3 positions for  (SL: True, TP: True)
2025-09-12 09:24:57,157 [DEBUG] DEBUG: Magic number usage - Ticket: 348075168, Magic: 2155, SL Points: 2155, Distance: 21.55000, Symbol: XAUUSD.iux
2025-09-12 09:24:57,158 [INFO] \U0001f522 #348075168: Magic=2155, SL Points=2155
2025-09-12 09:24:57,158 [DEBUG] #348075168: Entry=3593.70000, Current=3649.59000, SL=3594.20000, TP=3695.20000
2025-09-12 09:24:57,159 [DEBUG] #348075168: point_sl=21.55000, point_be=-3578.29800, is_moving_tp=True
2025-09-12 09:24:57,159 [DEBUG] #348075168: BUY Moving TP - Current: 3649.59000, Trigger: 15.40200
2025-09-12 09:24:57,159 [DEBUG] #348075168: BUY Moving TP factor -166.04631090487237 - New SL: -27.59800, New TP: 59.00200
2025-09-12 09:24:57,160 [DEBUG] #348075168: Modifying order - SL: 3594.20000 -> -27.59800, TP: 3695.20000 -> 59.00200
2025-09-12 09:24:57,179 [ERROR] #348075168 - ERROR in order modification: Order send failed: AutoTrading disabled by client
NoneType: None
2025-09-12 09:24:57,275 [INFO] \u274c #348075168 - ERROR in order modification: Order send failed: AutoTrading disabled by client
2025-09-12 09:24:57,295 [DEBUG] DEBUG: Magic number usage - Ticket: 351201149, Magic: 2800, SL Points: 2800, Distance: 28.00000, Symbol: XAUUSD.iux
2025-09-12 09:24:57,297 [INFO] \U0001f522 #351201149: Magic=2800, SL Points=2800
2025-09-12 09:24:57,298 [DEBUG] #351201149: Entry=3633.00000, Current=3649.59000, SL=3605.00000, TP=3655.00000
2025-09-12 09:24:57,298 [DEBUG] #351201149: point_sl=28.00000, point_be=-3624.45000, is_moving_tp=True
2025-09-12 09:24:57,298 [DEBUG] #351201149: BUY Moving TP - Current: 3649.59000, Trigger: 8.55000
2025-09-12 09:24:57,298 [DEBUG] #351201149: BUY Moving TP factor -129.44464285714287 - New SL: -47.35000, New TP: 65.05000
2025-09-12 09:24:57,299 [DEBUG] #351201149: Modifying order - SL: 3605.00000 -> -47.35000, TP: 3655.00000 -> 65.05000
2025-09-12 09:24:57,318 [ERROR] #351201149 - ERROR in order modification: Order send failed: AutoTrading disabled by client
NoneType: None
2025-09-12 09:24:57,350 [INFO] \u274c #351201149 - ERROR in order modification: Order send failed: AutoTrading disabled by client
2025-09-12 09:24:57,370 [DEBUG] DEBUG: Magic number usage - Ticket: 351201151, Magic: 2800, SL Points: 2800, Distance: 28.00000, Symbol: XAUUSD.iux
2025-09-12 09:24:57,371 [INFO] \U0001f522 #351201151: Magic=2800, SL Points=2800
2025-09-12 09:24:57,371 [DEBUG] #351201151: Entry=3633.00000, Current=3649.59000, SL=3605.00000, TP=3665.00000
2025-09-12 09:24:57,372 [DEBUG] #351201151: point_sl=28.00000, point_be=-3624.35000, is_moving_tp=True
2025-09-12 09:24:57,372 [DEBUG] #351201151: BUY Moving TP - Current: 3649.59000, Trigger: 8.65000
2025-09-12 09:24:57,372 [DEBUG] #351201151: BUY Moving TP factor -129.44107142857143 - New SL: -47.25000, New TP: 65.15000
2025-09-12 09:24:57,374 [DEBUG] #351201151: Modifying order - SL: 3605.00000 -> -47.25000, TP: 3665.00000 -> 65.15000
2025-09-12 09:24:57,392 [ERROR] #351201151 - ERROR in order modification: Order send failed: AutoTrading disabled by client
NoneType: None
2025-09-12 09:24:57,497 [INFO] \u274c #351201151 - ERROR in order modification: Order send failed: AutoTrading disabled by client
2025-09-12 09:24:57,498 [INFO] DEBUG: Order processing - Symbol: XAUUSD.iux, Group: , Total: 3, Processed: 3, Action: SL+TP
2025-09-12 09:24:57,498 [INFO] \U0001f4e6 SL+TP Processing: XAUUSD.iux (All) - 3/3 orders
2025-09-12 09:24:57,498 [DEBUG] Processed 3  positions for SL+TP
2025-09-12 09:25:27,569 [DEBUG] Processing auto BE/TP for 1 symbols: {'XAUUSD.iux'}
2025-09-12 09:25:27,569 [DEBUG] Processing 3 positions for XAUUSD.iux
2025-09-12 09:25:27,569 [DEBUG] Processing 3 positions for  (SL: True, TP: True)
2025-09-12 09:25:27,606 [DEBUG] DEBUG: Magic number usage - Ticket: 348075168, Magic: 2155, SL Points: 2155, Distance: 21.55000, Symbol: XAUUSD.iux
2025-09-12 09:25:27,608 [INFO] \U0001f522 #348075168: Magic=2155, SL Points=2155
2025-09-12 09:25:27,608 [DEBUG] #348075168: Entry=3593.70000, Current=3649.21000, SL=3594.20000, TP=3695.20000
2025-09-12 09:25:27,609 [DEBUG] #348075168: point_sl=21.55000, point_be=-3578.29800, is_moving_tp=True
2025-09-12 09:25:27,609 [DEBUG] #348075168: BUY Moving TP - Current: 3649.21000, Trigger: 15.40200
2025-09-12 09:25:27,610 [DEBUG] #348075168: BUY Moving TP factor -166.04631090487237 - New SL: -27.59800, New TP: 59.00200
2025-09-12 09:25:27,610 [DEBUG] #348075168: Modifying order - SL: 3594.20000 -> -27.59800, TP: 3695.20000 -> 59.00200
2025-09-12 09:25:27,629 [ERROR] #348075168 - ERROR in order modification: Order send failed: AutoTrading disabled by client
NoneType: None
2025-09-12 09:25:27,634 [INFO] \u274c #348075168 - ERROR in order modification: Order send failed: AutoTrading disabled by client
2025-09-12 09:25:27,654 [DEBUG] DEBUG: Magic number usage - Ticket: 351201149, Magic: 2800, SL Points: 2800, Distance: 28.00000, Symbol: XAUUSD.iux
2025-09-12 09:25:27,758 [INFO] \U0001f522 #351201149: Magic=2800, SL Points=2800
2025-09-12 09:25:27,758 [DEBUG] #351201149: Entry=3633.00000, Current=3649.21000, SL=3605.00000, TP=3655.00000
2025-09-12 09:25:27,759 [DEBUG] #351201149: point_sl=28.00000, point_be=-3624.45000, is_moving_tp=True
2025-09-12 09:25:27,759 [DEBUG] #351201149: BUY Moving TP - Current: 3649.21000, Trigger: 8.55000
2025-09-12 09:25:27,759 [DEBUG] #351201149: BUY Moving TP factor -129.44464285714287 - New SL: -47.35000, New TP: 65.05000
2025-09-12 09:25:27,760 [DEBUG] #351201149: Modifying order - SL: 3605.00000 -> -47.35000, TP: 3655.00000 -> 65.05000
2025-09-12 09:25:27,779 [ERROR] #351201149 - ERROR in order modification: Order send failed: AutoTrading disabled by client
NoneType: None
2025-09-12 09:25:27,883 [INFO] \u274c #351201149 - ERROR in order modification: Order send failed: AutoTrading disabled by client
2025-09-12 09:25:27,901 [DEBUG] DEBUG: Magic number usage - Ticket: 351201151, Magic: 2800, SL Points: 2800, Distance: 28.00000, Symbol: XAUUSD.iux
2025-09-12 09:25:27,903 [INFO] \U0001f522 #351201151: Magic=2800, SL Points=2800
2025-09-12 09:25:27,904 [DEBUG] #351201151: Entry=3633.00000, Current=3649.21000, SL=3605.00000, TP=3665.00000
2025-09-12 09:25:27,904 [DEBUG] #351201151: point_sl=28.00000, point_be=-3624.35000, is_moving_tp=True
2025-09-12 09:25:27,904 [DEBUG] #351201151: BUY Moving TP - Current: 3649.21000, Trigger: 8.65000
2025-09-12 09:25:27,905 [DEBUG] #351201151: BUY Moving TP factor -129.44107142857143 - New SL: -47.25000, New TP: 65.15000
2025-09-12 09:25:27,905 [DEBUG] #351201151: Modifying order - SL: 3605.00000 -> -47.25000, TP: 3665.00000 -> 65.15000
2025-09-12 09:25:27,924 [ERROR] #351201151 - ERROR in order modification: Order send failed: AutoTrading disabled by client
NoneType: None
2025-09-12 09:25:28,035 [INFO] \u274c #351201151 - ERROR in order modification: Order send failed: AutoTrading disabled by client
2025-09-12 09:25:28,035 [INFO] DEBUG: Order processing - Symbol: XAUUSD.iux, Group: , Total: 3, Processed: 3, Action: SL+TP
2025-09-12 09:25:28,037 [INFO] \U0001f4e6 SL+TP Processing: XAUUSD.iux (All) - 3/3 orders
2025-09-12 09:25:28,037 [DEBUG] Processed 3  positions for SL+TP
2025-09-12 09:25:58,108 [DEBUG] Processing auto BE/TP for 1 symbols: {'XAUUSD.iux'}
2025-09-12 09:25:58,108 [DEBUG] Processing 3 positions for XAUUSD.iux
2025-09-12 09:25:58,108 [DEBUG] Processing 3 positions for  (SL: True, TP: True)
2025-09-12 09:25:58,148 [DEBUG] DEBUG: Magic number usage - Ticket: 348075168, Magic: 2155, SL Points: 2155, Distance: 21.55000, Symbol: XAUUSD.iux
2025-09-12 09:25:58,148 [INFO] \U0001f522 #348075168: Magic=2155, SL Points=2155
2025-09-12 09:25:58,149 [DEBUG] #348075168: Entry=3593.70000, Current=3649.37000, SL=3594.20000, TP=3695.20000
2025-09-12 09:25:58,149 [DEBUG] #348075168: point_sl=21.55000, point_be=-3578.29800, is_moving_tp=True
2025-09-12 09:25:58,150 [DEBUG] #348075168: BUY Moving TP - Current: 3649.37000, Trigger: 15.40200
2025-09-12 09:25:58,150 [DEBUG] #348075168: BUY Moving TP factor -166.04631090487237 - New SL: -27.59800, New TP: 59.00200
2025-09-12 09:25:58,150 [DEBUG] #348075168: Modifying order - SL: 3594.20000 -> -27.59800, TP: 3695.20000 -> 59.00200
2025-09-12 09:25:58,169 [ERROR] #348075168 - ERROR in order modification: Order send failed: AutoTrading disabled by client
NoneType: None
2025-09-12 09:25:58,175 [INFO] \u274c #348075168 - ERROR in order modification: Order send failed: AutoTrading disabled by client
2025-09-12 09:25:58,195 [DEBUG] DEBUG: Magic number usage - Ticket: 351201149, Magic: 2800, SL Points: 2800, Distance: 28.00000, Symbol: XAUUSD.iux
2025-09-12 09:25:58,282 [INFO] \U0001f522 #351201149: Magic=2800, SL Points=2800
2025-09-12 09:25:58,283 [DEBUG] #351201149: Entry=3633.00000, Current=3649.37000, SL=3605.00000, TP=3655.00000
2025-09-12 09:25:58,283 [DEBUG] #351201149: point_sl=28.00000, point_be=-3624.45000, is_moving_tp=True
2025-09-12 09:25:58,283 [DEBUG] #351201149: BUY Moving TP - Current: 3649.37000, Trigger: 8.55000
2025-09-12 09:25:58,283 [DEBUG] #351201149: BUY Moving TP factor -129.44464285714287 - New SL: -47.35000, New TP: 65.05000
2025-09-12 09:25:58,284 [DEBUG] #351201149: Modifying order - SL: 3605.00000 -> -47.35000, TP: 3655.00000 -> 65.05000
2025-09-12 09:25:58,303 [ERROR] #351201149 - ERROR in order modification: Order send failed: AutoTrading disabled by client
NoneType: None
2025-09-12 09:25:58,407 [INFO] \u274c #351201149 - ERROR in order modification: Order send failed: AutoTrading disabled by client
2025-09-12 09:25:58,425 [DEBUG] DEBUG: Magic number usage - Ticket: 351201151, Magic: 2800, SL Points: 2800, Distance: 28.00000, Symbol: XAUUSD.iux
2025-09-12 09:25:58,426 [INFO] \U0001f522 #351201151: Magic=2800, SL Points=2800
2025-09-12 09:25:58,427 [DEBUG] #351201151: Entry=3633.00000, Current=3649.37000, SL=3605.00000, TP=3665.00000
2025-09-12 09:25:58,427 [DEBUG] #351201151: point_sl=28.00000, point_be=-3624.35000, is_moving_tp=True
2025-09-12 09:25:58,427 [DEBUG] #351201151: BUY Moving TP - Current: 3649.37000, Trigger: 8.65000
2025-09-12 09:25:58,428 [DEBUG] #351201151: BUY Moving TP factor -129.44107142857143 - New SL: -47.25000, New TP: 65.15000
2025-09-12 09:25:58,428 [DEBUG] #351201151: Modifying order - SL: 3605.00000 -> -47.25000, TP: 3665.00000 -> 65.15000
2025-09-12 09:25:58,448 [ERROR] #351201151 - ERROR in order modification: Order send failed: AutoTrading disabled by client
NoneType: None
2025-09-12 09:25:58,554 [INFO] \u274c #351201151 - ERROR in order modification: Order send failed: AutoTrading disabled by client
2025-09-12 09:25:58,555 [INFO] DEBUG: Order processing - Symbol: XAUUSD.iux, Group: , Total: 3, Processed: 3, Action: SL+TP
2025-09-12 09:25:58,556 [INFO] \U0001f4e6 SL+TP Processing: XAUUSD.iux (All) - 3/3 orders
2025-09-12 09:25:58,557 [DEBUG] Processed 3  positions for SL+TP
2025-09-12 09:26:28,611 [DEBUG] Processing auto BE/TP for 1 symbols: {'XAUUSD.iux'}
2025-09-12 09:26:28,611 [DEBUG] Processing 3 positions for XAUUSD.iux
2025-09-12 09:26:28,612 [DEBUG] Processing 3 positions for  (SL: True, TP: True)
2025-09-12 09:26:28,650 [DEBUG] DEBUG: Magic number usage - Ticket: 348075168, Magic: 2155, SL Points: 2155, Distance: 21.55000, Symbol: XAUUSD.iux
2025-09-12 09:26:28,652 [INFO] \U0001f522 #348075168: Magic=2155, SL Points=2155
2025-09-12 09:26:28,652 [DEBUG] #348075168: Entry=3593.70000, Current=3649.52000, SL=3594.20000, TP=3695.20000
2025-09-12 09:26:28,653 [DEBUG] #348075168: point_sl=21.55000, point_be=-3578.29800, is_moving_tp=True
2025-09-12 09:26:28,653 [DEBUG] #348075168: BUY Moving TP - Current: 3649.52000, Trigger: 15.40200
2025-09-12 09:26:28,654 [DEBUG] #348075168: BUY Moving TP factor -166.04631090487237 - New SL: -27.59800, New TP: 59.00200
2025-09-12 09:26:28,654 [DEBUG] #348075168: Modifying order - SL: 3594.20000 -> -27.59800, TP: 3695.20000 -> 59.00200
2025-09-12 09:26:28,671 [ERROR] #348075168 - ERROR in order modification: Order send failed: AutoTrading disabled by client
NoneType: None
2025-09-12 09:26:28,677 [INFO] \u274c #348075168 - ERROR in order modification: Order send failed: AutoTrading disabled by client
2025-09-12 09:26:28,697 [DEBUG] DEBUG: Magic number usage - Ticket: 351201149, Magic: 2800, SL Points: 2800, Distance: 28.00000, Symbol: XAUUSD.iux
2025-09-12 09:26:28,795 [INFO] \U0001f522 #351201149: Magic=2800, SL Points=2800
2025-09-12 09:26:28,796 [DEBUG] #351201149: Entry=3633.00000, Current=3649.52000, SL=3605.00000, TP=3655.00000
2025-09-12 09:26:28,796 [DEBUG] #351201149: point_sl=28.00000, point_be=-3624.45000, is_moving_tp=True
2025-09-12 09:26:28,797 [DEBUG] #351201149: BUY Moving TP - Current: 3649.52000, Trigger: 8.55000
2025-09-12 09:26:28,797 [DEBUG] #351201149: BUY Moving TP factor -129.44464285714287 - New SL: -47.35000, New TP: 65.05000
2025-09-12 09:26:28,797 [DEBUG] #351201149: Modifying order - SL: 3605.00000 -> -47.35000, TP: 3655.00000 -> 65.05000
2025-09-12 09:26:28,816 [ERROR] #351201149 - ERROR in order modification: Order send failed: AutoTrading disabled by client
NoneType: None
2025-09-12 09:26:28,921 [INFO] \u274c #351201149 - ERROR in order modification: Order send failed: AutoTrading disabled by client
2025-09-12 09:26:28,940 [DEBUG] DEBUG: Magic number usage - Ticket: 351201151, Magic: 2800, SL Points: 2800, Distance: 28.00000, Symbol: XAUUSD.iux
2025-09-12 09:26:29,051 [INFO] \U0001f522 #351201151: Magic=2800, SL Points=2800
2025-09-12 09:26:29,051 [DEBUG] #351201151: Entry=3633.00000, Current=3649.52000, SL=3605.00000, TP=3665.00000
2025-09-12 09:26:29,052 [DEBUG] #351201151: point_sl=28.00000, point_be=-3624.35000, is_moving_tp=True
2025-09-12 09:26:29,052 [DEBUG] #351201151: BUY Moving TP - Current: 3649.52000, Trigger: 8.65000
2025-09-12 09:26:29,053 [DEBUG] #351201151: BUY Moving TP factor -129.44107142857143 - New SL: -47.25000, New TP: 65.15000
2025-09-12 09:26:29,053 [DEBUG] #351201151: Modifying order - SL: 3605.00000 -> -47.25000, TP: 3665.00000 -> 65.15000
2025-09-12 09:26:29,072 [ERROR] #351201151 - ERROR in order modification: Order send failed: AutoTrading disabled by client
NoneType: None
2025-09-12 09:26:29,198 [INFO] \u274c #351201151 - ERROR in order modification: Order send failed: AutoTrading disabled by client
2025-09-12 09:26:29,199 [INFO] DEBUG: Order processing - Symbol: XAUUSD.iux, Group: , Total: 3, Processed: 3, Action: SL+TP
2025-09-12 09:26:29,201 [INFO] \U0001f4e6 SL+TP Processing: XAUUSD.iux (All) - 3/3 orders
2025-09-12 09:26:29,201 [DEBUG] Processed 3  positions for SL+TP
2025-09-12 09:26:59,263 [DEBUG] Processing auto BE/TP for 1 symbols: {'XAUUSD.iux'}
2025-09-12 09:26:59,263 [DEBUG] Processing 3 positions for XAUUSD.iux
2025-09-12 09:26:59,263 [DEBUG] Processing 3 positions for  (SL: True, TP: True)
2025-09-12 09:26:59,304 [DEBUG] DEBUG: Magic number usage - Ticket: 348075168, Magic: 2155, SL Points: 2155, Distance: 21.55000, Symbol: XAUUSD.iux
2025-09-12 09:26:59,304 [INFO] \U0001f522 #348075168: Magic=2155, SL Points=2155
2025-09-12 09:26:59,305 [DEBUG] #348075168: Entry=3593.70000, Current=3649.82000, SL=3594.20000, TP=3695.20000
2025-09-12 09:26:59,305 [DEBUG] #348075168: point_sl=21.55000, point_be=-3578.29800, is_moving_tp=True
2025-09-12 09:26:59,306 [DEBUG] #348075168: BUY Moving TP - Current: 3649.82000, Trigger: 15.40200
2025-09-12 09:26:59,306 [DEBUG] #348075168: BUY Moving TP factor -166.04631090487237 - New SL: -27.59800, New TP: 59.00200
2025-09-12 09:26:59,306 [DEBUG] #348075168: Modifying order - SL: 3594.20000 -> -27.59800, TP: 3695.20000 -> 59.00200
2025-09-12 09:26:59,325 [ERROR] #348075168 - ERROR in order modification: Order send failed: AutoTrading disabled by client
NoneType: None
2025-09-12 09:26:59,330 [INFO] \u274c #348075168 - ERROR in order modification: Order send failed: AutoTrading disabled by client
2025-09-12 09:26:59,349 [DEBUG] DEBUG: Magic number usage - Ticket: 351201149, Magic: 2800, SL Points: 2800, Distance: 28.00000, Symbol: XAUUSD.iux
2025-09-12 09:26:59,448 [INFO] \U0001f522 #351201149: Magic=2800, SL Points=2800
2025-09-12 09:26:59,448 [DEBUG] #351201149: Entry=3633.00000, Current=3649.82000, SL=3605.00000, TP=3655.00000
2025-09-12 09:26:59,449 [DEBUG] #351201149: point_sl=28.00000, point_be=-3624.45000, is_moving_tp=True
2025-09-12 09:26:59,449 [DEBUG] #351201149: BUY Moving TP - Current: 3649.82000, Trigger: 8.55000
2025-09-12 09:26:59,449 [DEBUG] #351201149: BUY Moving TP factor -129.44464285714287 - New SL: -47.35000, New TP: 65.05000
2025-09-12 09:26:59,450 [DEBUG] #351201149: Modifying order - SL: 3605.00000 -> -47.35000, TP: 3655.00000 -> 65.05000
2025-09-12 09:26:59,469 [ERROR] #351201149 - ERROR in order modification: Order send failed: AutoTrading disabled by client
NoneType: None
2025-09-12 09:26:59,587 [INFO] \u274c #351201149 - ERROR in order modification: Order send failed: AutoTrading disabled by client
2025-09-12 09:26:59,608 [DEBUG] DEBUG: Magic number usage - Ticket: 351201151, Magic: 2800, SL Points: 2800, Distance: 28.00000, Symbol: XAUUSD.iux
2025-09-12 09:26:59,610 [INFO] \U0001f522 #351201151: Magic=2800, SL Points=2800
2025-09-12 09:26:59,610 [DEBUG] #351201151: Entry=3633.00000, Current=3649.82000, SL=3605.00000, TP=3665.00000
2025-09-12 09:26:59,610 [DEBUG] #351201151: point_sl=28.00000, point_be=-3624.35000, is_moving_tp=True
2025-09-12 09:26:59,611 [DEBUG] #351201151: BUY Moving TP - Current: 3649.82000, Trigger: 8.65000
2025-09-12 09:26:59,611 [DEBUG] #351201151: BUY Moving TP factor -129.44107142857143 - New SL: -47.25000, New TP: 65.15000
2025-09-12 09:26:59,611 [DEBUG] #351201151: Modifying order - SL: 3605.00000 -> -47.25000, TP: 3665.00000 -> 65.15000
2025-09-12 09:26:59,631 [ERROR] #351201151 - ERROR in order modification: Order send failed: AutoTrading disabled by client
NoneType: None
2025-09-12 09:26:59,748 [INFO] \u274c #351201151 - ERROR in order modification: Order send failed: AutoTrading disabled by client
2025-09-12 09:26:59,748 [INFO] DEBUG: Order processing - Symbol: XAUUSD.iux, Group: , Total: 3, Processed: 3, Action: SL+TP
2025-09-12 09:26:59,750 [INFO] \U0001f4e6 SL+TP Processing: XAUUSD.iux (All) - 3/3 orders
2025-09-12 09:26:59,750 [DEBUG] Processed 3  positions for SL+TP
2025-09-12 09:27:29,808 [DEBUG] Processing auto BE/TP for 1 symbols: {'XAUUSD.iux'}
2025-09-12 09:27:29,809 [DEBUG] Processing 3 positions for XAUUSD.iux
2025-09-12 09:27:29,809 [DEBUG] Processing 3 positions for  (SL: True, TP: True)
2025-09-12 09:27:29,850 [DEBUG] DEBUG: Magic number usage - Ticket: 348075168, Magic: 2155, SL Points: 2155, Distance: 21.55000, Symbol: XAUUSD.iux
2025-09-12 09:27:29,850 [INFO] \U0001f522 #348075168: Magic=2155, SL Points=2155
2025-09-12 09:27:29,851 [DEBUG] #348075168: Entry=3593.70000, Current=3649.52000, SL=3594.20000, TP=3695.20000
2025-09-12 09:27:29,851 [DEBUG] #348075168: point_sl=21.55000, point_be=-3578.29800, is_moving_tp=True
2025-09-12 09:27:29,851 [DEBUG] #348075168: BUY Moving TP - Current: 3649.52000, Trigger: 15.40200
2025-09-12 09:27:29,852 [DEBUG] #348075168: BUY Moving TP factor -166.04631090487237 - New SL: -27.59800, New TP: 59.00200
2025-09-12 09:27:29,852 [DEBUG] #348075168: Modifying order - SL: 3594.20000 -> -27.59800, TP: 3695.20000 -> 59.00200
2025-09-12 09:27:29,871 [ERROR] #348075168 - ERROR in order modification: Order send failed: AutoTrading disabled by client
NoneType: None
2025-09-12 09:27:29,876 [INFO] \u274c #348075168 - ERROR in order modification: Order send failed: AutoTrading disabled by client
2025-09-12 09:27:29,896 [DEBUG] DEBUG: Magic number usage - Ticket: 351201149, Magic: 2800, SL Points: 2800, Distance: 28.00000, Symbol: XAUUSD.iux
2025-09-12 09:27:29,994 [INFO] \U0001f522 #351201149: Magic=2800, SL Points=2800
2025-09-12 09:27:29,994 [DEBUG] #351201149: Entry=3633.00000, Current=3649.52000, SL=3605.00000, TP=3655.00000
2025-09-12 09:27:29,994 [DEBUG] #351201149: point_sl=28.00000, point_be=-3624.45000, is_moving_tp=True
2025-09-12 09:27:29,995 [DEBUG] #351201149: BUY Moving TP - Current: 3649.52000, Trigger: 8.55000
2025-09-12 09:27:29,995 [DEBUG] #351201149: BUY Moving TP factor -129.44464285714287 - New SL: -47.35000, New TP: 65.05000
2025-09-12 09:27:29,995 [DEBUG] #351201149: Modifying order - SL: 3605.00000 -> -47.35000, TP: 3655.00000 -> 65.05000
2025-09-12 09:27:30,015 [ERROR] #351201149 - ERROR in order modification: Order send failed: AutoTrading disabled by client
NoneType: None
2025-09-12 09:27:30,125 [INFO] \u274c #351201149 - ERROR in order modification: Order send failed: AutoTrading disabled by client
2025-09-12 09:27:30,144 [DEBUG] DEBUG: Magic number usage - Ticket: 351201151, Magic: 2800, SL Points: 2800, Distance: 28.00000, Symbol: XAUUSD.iux
2025-09-12 09:27:30,146 [INFO] \U0001f522 #351201151: Magic=2800, SL Points=2800
2025-09-12 09:27:30,146 [DEBUG] #351201151: Entry=3633.00000, Current=3649.52000, SL=3605.00000, TP=3665.00000
2025-09-12 09:27:30,147 [DEBUG] #351201151: point_sl=28.00000, point_be=-3624.35000, is_moving_tp=True
2025-09-12 09:27:30,147 [DEBUG] #351201151: BUY Moving TP - Current: 3649.52000, Trigger: 8.65000
2025-09-12 09:27:30,147 [DEBUG] #351201151: BUY Moving TP factor -129.44107142857143 - New SL: -47.25000, New TP: 65.15000
2025-09-12 09:27:30,148 [DEBUG] #351201151: Modifying order - SL: 3605.00000 -> -47.25000, TP: 3665.00000 -> 65.15000
2025-09-12 09:27:30,166 [ERROR] #351201151 - ERROR in order modification: Order send failed: AutoTrading disabled by client
NoneType: None
2025-09-12 09:27:30,273 [INFO] \u274c #351201151 - ERROR in order modification: Order send failed: AutoTrading disabled by client
2025-09-12 09:27:30,274 [INFO] DEBUG: Order processing - Symbol: XAUUSD.iux, Group: , Total: 3, Processed: 3, Action: SL+TP
2025-09-12 09:27:30,277 [INFO] \U0001f4e6 SL+TP Processing: XAUUSD.iux (All) - 3/3 orders
2025-09-12 09:27:30,278 [DEBUG] Processed 3  positions for SL+TP
2025-09-12 09:28:00,330 [DEBUG] Processing auto BE/TP for 1 symbols: {'XAUUSD.iux'}
2025-09-12 09:28:00,330 [DEBUG] Processing 3 positions for XAUUSD.iux
2025-09-12 09:28:00,331 [DEBUG] Processing 3 positions for  (SL: True, TP: True)
2025-09-12 09:28:00,369 [DEBUG] DEBUG: Magic number usage - Ticket: 348075168, Magic: 2155, SL Points: 2155, Distance: 21.55000, Symbol: XAUUSD.iux
2025-09-12 09:28:00,370 [INFO] \U0001f522 #348075168: Magic=2155, SL Points=2155
2025-09-12 09:28:00,371 [DEBUG] #348075168: Entry=3593.70000, Current=3649.58000, SL=3594.20000, TP=3695.20000
2025-09-12 09:28:00,371 [DEBUG] #348075168: point_sl=21.55000, point_be=-3578.29800, is_moving_tp=True
2025-09-12 09:28:00,371 [DEBUG] #348075168: BUY Moving TP - Current: 3649.58000, Trigger: 15.40200
2025-09-12 09:28:00,372 [DEBUG] #348075168: BUY Moving TP factor -166.04631090487237 - New SL: -27.59800, New TP: 59.00200
2025-09-12 09:28:00,372 [DEBUG] #348075168: Modifying order - SL: 3594.20000 -> -27.59800, TP: 3695.20000 -> 59.00200
2025-09-12 09:28:00,390 [ERROR] #348075168 - ERROR in order modification: Order send failed: AutoTrading disabled by client
NoneType: None
2025-09-12 09:28:00,395 [INFO] \u274c #348075168 - ERROR in order modification: Order send failed: AutoTrading disabled by client
2025-09-12 09:28:00,414 [DEBUG] DEBUG: Magic number usage - Ticket: 351201149, Magic: 2800, SL Points: 2800, Distance: 28.00000, Symbol: XAUUSD.iux
2025-09-12 09:28:00,505 [INFO] \U0001f522 #351201149: Magic=2800, SL Points=2800
2025-09-12 09:28:00,506 [DEBUG] #351201149: Entry=3633.00000, Current=3649.58000, SL=3605.00000, TP=3655.00000
2025-09-12 09:28:00,506 [DEBUG] #351201149: point_sl=28.00000, point_be=-3624.45000, is_moving_tp=True
2025-09-12 09:28:00,506 [DEBUG] #351201149: BUY Moving TP - Current: 3649.58000, Trigger: 8.55000
2025-09-12 09:28:00,507 [DEBUG] #351201149: BUY Moving TP factor -129.44464285714287 - New SL: -47.35000, New TP: 65.05000
2025-09-12 09:28:00,507 [DEBUG] #351201149: Modifying order - SL: 3605.00000 -> -47.35000, TP: 3655.00000 -> 65.05000
2025-09-12 09:28:00,525 [ERROR] #351201149 - ERROR in order modification: Order send failed: AutoTrading disabled by client
NoneType: None
2025-09-12 09:28:00,629 [INFO] \u274c #351201149 - ERROR in order modification: Order send failed: AutoTrading disabled by client
2025-09-12 09:28:00,648 [DEBUG] DEBUG: Magic number usage - Ticket: 351201151, Magic: 2800, SL Points: 2800, Distance: 28.00000, Symbol: XAUUSD.iux
2025-09-12 09:28:00,649 [INFO] \U0001f522 #351201151: Magic=2800, SL Points=2800
2025-09-12 09:28:00,651 [DEBUG] #351201151: Entry=3633.00000, Current=3649.58000, SL=3605.00000, TP=3665.00000
2025-09-12 09:28:00,651 [DEBUG] #351201151: point_sl=28.00000, point_be=-3624.35000, is_moving_tp=True
2025-09-12 09:28:00,651 [DEBUG] #351201151: BUY Moving TP - Current: 3649.58000, Trigger: 8.65000
2025-09-12 09:28:00,651 [DEBUG] #351201151: BUY Moving TP factor -129.44107142857143 - New SL: -47.25000, New TP: 65.15000
2025-09-12 09:28:00,652 [DEBUG] #351201151: Modifying order - SL: 3605.00000 -> -47.25000, TP: 3665.00000 -> 65.15000
2025-09-12 09:28:00,675 [ERROR] #351201151 - ERROR in order modification: Order send failed: AutoTrading disabled by client
NoneType: None
2025-09-12 09:28:00,788 [INFO] \u274c #351201151 - ERROR in order modification: Order send failed: AutoTrading disabled by client
2025-09-12 09:28:00,788 [INFO] DEBUG: Order processing - Symbol: XAUUSD.iux, Group: , Total: 3, Processed: 3, Action: SL+TP
2025-09-12 09:28:00,790 [INFO] \U0001f4e6 SL+TP Processing: XAUUSD.iux (All) - 3/3 orders
2025-09-12 09:28:00,790 [DEBUG] Processed 3  positions for SL+TP
2025-09-12 09:28:30,848 [DEBUG] Processing auto BE/TP for 1 symbols: {'XAUUSD.iux'}
2025-09-12 09:28:30,849 [DEBUG] Processing 3 positions for XAUUSD.iux
2025-09-12 09:28:30,849 [DEBUG] Processing 3 positions for  (SL: True, TP: True)
2025-09-12 09:28:30,890 [DEBUG] DEBUG: Magic number usage - Ticket: 348075168, Magic: 2155, SL Points: 2155, Distance: 21.55000, Symbol: XAUUSD.iux
2025-09-12 09:28:30,890 [INFO] \U0001f522 #348075168: Magic=2155, SL Points=2155
2025-09-12 09:28:30,891 [DEBUG] #348075168: Entry=3593.70000, Current=3649.68000, SL=3594.20000, TP=3695.20000
2025-09-12 09:28:30,891 [DEBUG] #348075168: point_sl=21.55000, point_be=-3578.29800, is_moving_tp=True
2025-09-12 09:28:30,892 [DEBUG] #348075168: BUY Moving TP - Current: 3649.68000, Trigger: 15.40200
2025-09-12 09:28:30,892 [DEBUG] #348075168: BUY Moving TP factor -166.04631090487237 - New SL: -27.59800, New TP: 59.00200
2025-09-12 09:28:30,892 [DEBUG] #348075168: Modifying order - SL: 3594.20000 -> -27.59800, TP: 3695.20000 -> 59.00200
2025-09-12 09:28:30,911 [ERROR] #348075168 - ERROR in order modification: Order send failed: AutoTrading disabled by client
NoneType: None
2025-09-12 09:28:30,916 [INFO] \u274c #348075168 - ERROR in order modification: Order send failed: AutoTrading disabled by client
2025-09-12 09:28:30,936 [DEBUG] DEBUG: Magic number usage - Ticket: 351201149, Magic: 2800, SL Points: 2800, Distance: 28.00000, Symbol: XAUUSD.iux
2025-09-12 09:28:31,025 [INFO] \U0001f522 #351201149: Magic=2800, SL Points=2800
2025-09-12 09:28:31,027 [DEBUG] #351201149: Entry=3633.00000, Current=3649.68000, SL=3605.00000, TP=3655.00000
2025-09-12 09:28:31,027 [DEBUG] #351201149: point_sl=28.00000, point_be=-3624.45000, is_moving_tp=True
2025-09-12 09:28:31,027 [DEBUG] #351201149: BUY Moving TP - Current: 3649.68000, Trigger: 8.55000
2025-09-12 09:28:31,028 [DEBUG] #351201149: BUY Moving TP factor -129.44464285714287 - New SL: -47.35000, New TP: 65.05000
2025-09-12 09:28:31,028 [DEBUG] #351201149: Modifying order - SL: 3605.00000 -> -47.35000, TP: 3655.00000 -> 65.05000
2025-09-12 09:28:31,045 [ERROR] #351201149 - ERROR in order modification: Order send failed: AutoTrading disabled by client
NoneType: None
2025-09-12 09:28:31,150 [INFO] \u274c #351201149 - ERROR in order modification: Order send failed: AutoTrading disabled by client
2025-09-12 09:28:31,168 [DEBUG] DEBUG: Magic number usage - Ticket: 351201151, Magic: 2800, SL Points: 2800, Distance: 28.00000, Symbol: XAUUSD.iux
2025-09-12 09:28:31,170 [INFO] \U0001f522 #351201151: Magic=2800, SL Points=2800
2025-09-12 09:28:31,171 [DEBUG] #351201151: Entry=3633.00000, Current=3649.68000, SL=3605.00000, TP=3665.00000
2025-09-12 09:28:31,171 [DEBUG] #351201151: point_sl=28.00000, point_be=-3624.35000, is_moving_tp=True
2025-09-12 09:28:31,171 [DEBUG] #351201151: BUY Moving TP - Current: 3649.68000, Trigger: 8.65000
2025-09-12 09:28:31,172 [DEBUG] #351201151: BUY Moving TP factor -129.44107142857143 - New SL: -47.25000, New TP: 65.15000
2025-09-12 09:28:31,172 [DEBUG] #351201151: Modifying order - SL: 3605.00000 -> -47.25000, TP: 3665.00000 -> 65.15000
2025-09-12 09:28:31,191 [ERROR] #351201151 - ERROR in order modification: Order send failed: AutoTrading disabled by client
NoneType: None
2025-09-12 09:28:31,302 [INFO] \u274c #351201151 - ERROR in order modification: Order send failed: AutoTrading disabled by client
2025-09-12 09:28:31,303 [INFO] DEBUG: Order processing - Symbol: XAUUSD.iux, Group: , Total: 3, Processed: 3, Action: SL+TP
2025-09-12 09:28:31,304 [INFO] \U0001f4e6 SL+TP Processing: XAUUSD.iux (All) - 3/3 orders
2025-09-12 09:28:31,305 [DEBUG] Processed 3  positions for SL+TP
2025-09-12 09:29:01,367 [DEBUG] Processing auto BE/TP for 1 symbols: {'XAUUSD.iux'}
2025-09-12 09:29:01,367 [DEBUG] Processing 3 positions for XAUUSD.iux
2025-09-12 09:29:01,368 [DEBUG] Processing 3 positions for  (SL: True, TP: True)
2025-09-12 09:29:01,406 [DEBUG] DEBUG: Magic number usage - Ticket: 348075168, Magic: 2155, SL Points: 2155, Distance: 21.55000, Symbol: XAUUSD.iux
2025-09-12 09:29:01,407 [INFO] \U0001f522 #348075168: Magic=2155, SL Points=2155
2025-09-12 09:29:01,407 [DEBUG] #348075168: Entry=3593.70000, Current=3649.84000, SL=3594.20000, TP=3695.20000
2025-09-12 09:29:01,408 [DEBUG] #348075168: point_sl=21.55000, point_be=-3578.29800, is_moving_tp=True
2025-09-12 09:29:01,408 [DEBUG] #348075168: BUY Moving TP - Current: 3649.84000, Trigger: 15.40200
2025-09-12 09:29:01,408 [DEBUG] #348075168: BUY Moving TP factor -166.04631090487237 - New SL: -27.59800, New TP: 59.00200
2025-09-12 09:29:01,409 [DEBUG] #348075168: Modifying order - SL: 3594.20000 -> -27.59800, TP: 3695.20000 -> 59.00200
2025-09-12 09:29:01,427 [ERROR] #348075168 - ERROR in order modification: Order send failed: AutoTrading disabled by client
NoneType: None
2025-09-12 09:29:01,432 [INFO] \u274c #348075168 - ERROR in order modification: Order send failed: AutoTrading disabled by client
2025-09-12 09:29:01,451 [DEBUG] DEBUG: Magic number usage - Ticket: 351201149, Magic: 2800, SL Points: 2800, Distance: 28.00000, Symbol: XAUUSD.iux
2025-09-12 09:29:01,547 [INFO] \U0001f522 #351201149: Magic=2800, SL Points=2800
2025-09-12 09:29:01,547 [DEBUG] #351201149: Entry=3633.00000, Current=3649.84000, SL=3605.00000, TP=3655.00000
2025-09-12 09:29:01,547 [DEBUG] #351201149: point_sl=28.00000, point_be=-3624.45000, is_moving_tp=True
2025-09-12 09:29:01,548 [DEBUG] #351201149: BUY Moving TP - Current: 3649.84000, Trigger: 8.55000
2025-09-12 09:29:01,548 [DEBUG] #351201149: BUY Moving TP factor -129.44464285714287 - New SL: -47.35000, New TP: 65.05000
2025-09-12 09:29:01,548 [DEBUG] #351201149: Modifying order - SL: 3605.00000 -> -47.35000, TP: 3655.00000 -> 65.05000
2025-09-12 09:29:01,568 [ERROR] #351201149 - ERROR in order modification: Order send failed: AutoTrading disabled by client
NoneType: None
2025-09-12 09:29:01,686 [INFO] \u274c #351201149 - ERROR in order modification: Order send failed: AutoTrading disabled by client
2025-09-12 09:29:01,705 [DEBUG] DEBUG: Magic number usage - Ticket: 351201151, Magic: 2800, SL Points: 2800, Distance: 28.00000, Symbol: XAUUSD.iux
2025-09-12 09:29:01,706 [INFO] \U0001f522 #351201151: Magic=2800, SL Points=2800
2025-09-12 09:29:01,706 [DEBUG] #351201151: Entry=3633.00000, Current=3649.84000, SL=3605.00000, TP=3665.00000
2025-09-12 09:29:01,707 [DEBUG] #351201151: point_sl=28.00000, point_be=-3624.35000, is_moving_tp=True
2025-09-12 09:29:01,707 [DEBUG] #351201151: BUY Moving TP - Current: 3649.84000, Trigger: 8.65000
2025-09-12 09:29:01,707 [DEBUG] #351201151: BUY Moving TP factor -129.44107142857143 - New SL: -47.25000, New TP: 65.15000
2025-09-12 09:29:01,708 [DEBUG] #351201151: Modifying order - SL: 3605.00000 -> -47.25000, TP: 3665.00000 -> 65.15000
2025-09-12 09:29:01,725 [ERROR] #351201151 - ERROR in order modification: Order send failed: AutoTrading disabled by client
NoneType: None
2025-09-12 09:29:01,832 [INFO] \u274c #351201151 - ERROR in order modification: Order send failed: AutoTrading disabled by client
2025-09-12 09:29:01,833 [INFO] DEBUG: Order processing - Symbol: XAUUSD.iux, Group: , Total: 3, Processed: 3, Action: SL+TP
2025-09-12 09:29:01,834 [INFO] \U0001f4e6 SL+TP Processing: XAUUSD.iux (All) - 3/3 orders
2025-09-12 09:29:01,835 [DEBUG] Processed 3  positions for SL+TP
2025-09-12 09:29:31,899 [DEBUG] Processing auto BE/TP for 1 symbols: {'XAUUSD.iux'}
2025-09-12 09:29:31,899 [DEBUG] Processing 3 positions for XAUUSD.iux
2025-09-12 09:29:31,901 [DEBUG] Processing 3 positions for  (SL: True, TP: True)
2025-09-12 09:29:31,943 [DEBUG] DEBUG: Magic number usage - Ticket: 348075168, Magic: 2155, SL Points: 2155, Distance: 21.55000, Symbol: XAUUSD.iux
2025-09-12 09:29:31,944 [INFO] \U0001f522 #348075168: Magic=2155, SL Points=2155
2025-09-12 09:29:31,944 [DEBUG] #348075168: Entry=3593.70000, Current=3649.70000, SL=3594.20000, TP=3695.20000
2025-09-12 09:29:31,945 [DEBUG] #348075168: point_sl=21.55000, point_be=-3578.29800, is_moving_tp=True
2025-09-12 09:29:31,945 [DEBUG] #348075168: BUY Moving TP - Current: 3649.70000, Trigger: 15.40200
2025-09-12 09:29:31,945 [DEBUG] #348075168: BUY Moving TP factor -166.04631090487237 - New SL: -27.59800, New TP: 59.00200
2025-09-12 09:29:31,946 [DEBUG] #348075168: Modifying order - SL: 3594.20000 -> -27.59800, TP: 3695.20000 -> 59.00200
2025-09-12 09:29:31,967 [ERROR] #348075168 - ERROR in order modification: Order send failed: AutoTrading disabled by client
NoneType: None
2025-09-12 09:29:32,075 [INFO] \u274c #348075168 - ERROR in order modification: Order send failed: AutoTrading disabled by client
2025-09-12 09:29:32,094 [DEBUG] DEBUG: Magic number usage - Ticket: 351201149, Magic: 2800, SL Points: 2800, Distance: 28.00000, Symbol: XAUUSD.iux
2025-09-12 09:29:32,096 [INFO] \U0001f522 #351201149: Magic=2800, SL Points=2800
2025-09-12 09:29:32,096 [DEBUG] #351201149: Entry=3633.00000, Current=3649.70000, SL=3605.00000, TP=3655.00000
2025-09-12 09:29:32,097 [DEBUG] #351201149: point_sl=28.00000, point_be=-3624.45000, is_moving_tp=True
2025-09-12 09:29:32,097 [DEBUG] #351201149: BUY Moving TP - Current: 3649.70000, Trigger: 8.55000
2025-09-12 09:29:32,097 [DEBUG] #351201149: BUY Moving TP factor -129.44464285714287 - New SL: -47.35000, New TP: 65.05000
2025-09-12 09:29:32,098 [DEBUG] #351201149: Modifying order - SL: 3605.00000 -> -47.35000, TP: 3655.00000 -> 65.05000
2025-09-12 09:29:32,118 [ERROR] #351201149 - ERROR in order modification: Order send failed: AutoTrading disabled by client
NoneType: None
2025-09-12 09:29:32,143 [INFO] \u274c #351201149 - ERROR in order modification: Order send failed: AutoTrading disabled by client
2025-09-12 09:29:32,161 [DEBUG] DEBUG: Magic number usage - Ticket: 351201151, Magic: 2800, SL Points: 2800, Distance: 28.00000, Symbol: XAUUSD.iux
2025-09-12 09:29:32,163 [INFO] \U0001f522 #351201151: Magic=2800, SL Points=2800
2025-09-12 09:29:32,163 [DEBUG] #351201151: Entry=3633.00000, Current=3649.70000, SL=3605.00000, TP=3665.00000
2025-09-12 09:29:32,164 [DEBUG] #351201151: point_sl=28.00000, point_be=-3624.35000, is_moving_tp=True
2025-09-12 09:29:32,164 [DEBUG] #351201151: BUY Moving TP - Current: 3649.70000, Trigger: 8.65000
2025-09-12 09:29:32,164 [DEBUG] #351201151: BUY Moving TP factor -129.44107142857143 - New SL: -47.25000, New TP: 65.15000
2025-09-12 09:29:32,165 [DEBUG] #351201151: Modifying order - SL: 3605.00000 -> -47.25000, TP: 3665.00000 -> 65.15000
2025-09-12 09:29:32,184 [ERROR] #351201151 - ERROR in order modification: Order send failed: AutoTrading disabled by client
NoneType: None
2025-09-12 09:29:32,286 [INFO] \u274c #351201151 - ERROR in order modification: Order send failed: AutoTrading disabled by client
2025-09-12 09:29:32,286 [INFO] DEBUG: Order processing - Symbol: XAUUSD.iux, Group: , Total: 3, Processed: 3, Action: SL+TP
2025-09-12 09:29:32,288 [INFO] \U0001f4e6 SL+TP Processing: XAUUSD.iux (All) - 3/3 orders
2025-09-12 09:29:32,289 [DEBUG] Processed 3  positions for SL+TP
2025-09-12 09:30:02,353 [DEBUG] Processing auto BE/TP for 1 symbols: {'XAUUSD.iux'}
2025-09-12 09:30:02,353 [DEBUG] Processing 3 positions for XAUUSD.iux
2025-09-12 09:30:02,353 [DEBUG] Processing 3 positions for  (SL: True, TP: True)
2025-09-12 09:30:02,396 [DEBUG] DEBUG: Magic number usage - Ticket: 348075168, Magic: 2155, SL Points: 2155, Distance: 21.55000, Symbol: XAUUSD.iux
2025-09-12 09:30:02,396 [INFO] \U0001f522 #348075168: Magic=2155, SL Points=2155
2025-09-12 09:30:02,397 [DEBUG] #348075168: Entry=3593.70000, Current=3650.11000, SL=3594.20000, TP=3695.20000
2025-09-12 09:30:02,398 [DEBUG] #348075168: point_sl=21.55000, point_be=-3578.29800, is_moving_tp=True
2025-09-12 09:30:02,398 [DEBUG] #348075168: BUY Moving TP - Current: 3650.11000, Trigger: 15.40200
2025-09-12 09:30:02,398 [DEBUG] #348075168: BUY Moving TP factor -166.04631090487237 - New SL: -27.59800, New TP: 59.00200
2025-09-12 09:30:02,399 [DEBUG] #348075168: Modifying order - SL: 3594.20000 -> -27.59800, TP: 3695.20000 -> 59.00200
2025-09-12 09:30:02,418 [ERROR] #348075168 - ERROR in order modification: Order send failed: AutoTrading disabled by client
NoneType: None
2025-09-12 09:30:02,423 [INFO] \u274c #348075168 - ERROR in order modification: Order send failed: AutoTrading disabled by client
2025-09-12 09:30:02,442 [DEBUG] DEBUG: Magic number usage - Ticket: 351201149, Magic: 2800, SL Points: 2800, Distance: 28.00000, Symbol: XAUUSD.iux
2025-09-12 09:30:02,555 [INFO] \U0001f522 #351201149: Magic=2800, SL Points=2800
2025-09-12 09:30:02,555 [DEBUG] #351201149: Entry=3633.00000, Current=3650.11000, SL=3605.00000, TP=3655.00000
2025-09-12 09:30:02,556 [DEBUG] #351201149: point_sl=28.00000, point_be=-3624.45000, is_moving_tp=True
2025-09-12 09:30:02,556 [DEBUG] #351201149: BUY Moving TP - Current: 3650.11000, Trigger: 8.55000
2025-09-12 09:30:02,556 [DEBUG] #351201149: BUY Moving TP factor -129.44464285714287 - New SL: -47.35000, New TP: 65.05000
2025-09-12 09:30:02,556 [DEBUG] #351201149: Modifying order - SL: 3605.00000 -> -47.35000, TP: 3655.00000 -> 65.05000
2025-09-12 09:30:02,575 [ERROR] #351201149 - ERROR in order modification: Order send failed: AutoTrading disabled by client
NoneType: None
2025-09-12 09:30:02,692 [INFO] \u274c #351201149 - ERROR in order modification: Order send failed: AutoTrading disabled by client
2025-09-12 09:30:02,712 [DEBUG] DEBUG: Magic number usage - Ticket: 351201151, Magic: 2800, SL Points: 2800, Distance: 28.00000, Symbol: XAUUSD.iux
2025-09-12 09:30:02,715 [INFO] \U0001f522 #351201151: Magic=2800, SL Points=2800
2025-09-12 09:30:02,715 [DEBUG] #351201151: Entry=3633.00000, Current=3650.11000, SL=3605.00000, TP=3665.00000
2025-09-12 09:30:02,715 [DEBUG] #351201151: point_sl=28.00000, point_be=-3624.35000, is_moving_tp=True
2025-09-12 09:30:02,716 [DEBUG] #351201151: BUY Moving TP - Current: 3650.11000, Trigger: 8.65000
2025-09-12 09:30:02,716 [DEBUG] #351201151: BUY Moving TP factor -129.44107142857143 - New SL: -47.25000, New TP: 65.15000
2025-09-12 09:30:02,716 [DEBUG] #351201151: Modifying order - SL: 3605.00000 -> -47.25000, TP: 3665.00000 -> 65.15000
2025-09-12 09:30:02,734 [ERROR] #351201151 - ERROR in order modification: Order send failed: AutoTrading disabled by client
NoneType: None
2025-09-12 09:30:02,851 [INFO] \u274c #351201151 - ERROR in order modification: Order send failed: AutoTrading disabled by client
2025-09-12 09:30:02,851 [INFO] DEBUG: Order processing - Symbol: XAUUSD.iux, Group: , Total: 3, Processed: 3, Action: SL+TP
2025-09-12 09:30:02,852 [INFO] \U0001f4e6 SL+TP Processing: XAUUSD.iux (All) - 3/3 orders
2025-09-12 09:30:02,853 [DEBUG] Processed 3  positions for SL+TP
2025-09-12 09:30:32,916 [DEBUG] Processing auto BE/TP for 1 symbols: {'XAUUSD.iux'}
2025-09-12 09:30:32,916 [DEBUG] Processing 3 positions for XAUUSD.iux
2025-09-12 09:30:32,917 [DEBUG] Processing 3 positions for  (SL: True, TP: True)
2025-09-12 09:30:32,956 [DEBUG] DEBUG: Magic number usage - Ticket: 348075168, Magic: 2155, SL Points: 2155, Distance: 21.55000, Symbol: XAUUSD.iux
2025-09-12 09:30:32,956 [INFO] \U0001f522 #348075168: Magic=2155, SL Points=2155
2025-09-12 09:30:32,957 [DEBUG] #348075168: Entry=3593.70000, Current=3650.38000, SL=3594.20000, TP=3695.20000
2025-09-12 09:30:32,957 [DEBUG] #348075168: point_sl=21.55000, point_be=-3578.29800, is_moving_tp=True
2025-09-12 09:30:32,958 [DEBUG] #348075168: BUY Moving TP - Current: 3650.38000, Trigger: 15.40200
2025-09-12 09:30:32,958 [DEBUG] #348075168: BUY Moving TP factor -166.04631090487237 - New SL: -27.59800, New TP: 59.00200
2025-09-12 09:30:32,958 [DEBUG] #348075168: Modifying order - SL: 3594.20000 -> -27.59800, TP: 3695.20000 -> 59.00200
2025-09-12 09:30:32,977 [ERROR] #348075168 - ERROR in order modification: Order send failed: AutoTrading disabled by client
NoneType: None
2025-09-12 09:30:32,982 [INFO] \u274c #348075168 - ERROR in order modification: Order send failed: AutoTrading disabled by client
2025-09-12 09:30:33,002 [DEBUG] DEBUG: Magic number usage - Ticket: 351201149, Magic: 2800, SL Points: 2800, Distance: 28.00000, Symbol: XAUUSD.iux
2025-09-12 09:30:33,088 [INFO] \U0001f522 #351201149: Magic=2800, SL Points=2800
2025-09-12 09:30:33,089 [DEBUG] #351201149: Entry=3633.00000, Current=3650.38000, SL=3605.00000, TP=3655.00000
2025-09-12 09:30:33,089 [DEBUG] #351201149: point_sl=28.00000, point_be=-3624.45000, is_moving_tp=True
2025-09-12 09:30:33,089 [DEBUG] #351201149: BUY Moving TP - Current: 3650.38000, Trigger: 8.55000
2025-09-12 09:30:33,090 [DEBUG] #351201149: BUY Moving TP factor -129.44464285714287 - New SL: -47.35000, New TP: 65.05000
2025-09-12 09:30:33,090 [DEBUG] #351201149: Modifying order - SL: 3605.00000 -> -47.35000, TP: 3655.00000 -> 65.05000
2025-09-12 09:30:33,108 [ERROR] #351201149 - ERROR in order modification: Order send failed: AutoTrading disabled by client
NoneType: None
2025-09-12 09:30:33,215 [INFO] \u274c #351201149 - ERROR in order modification: Order send failed: AutoTrading disabled by client
2025-09-12 09:30:33,238 [DEBUG] DEBUG: Magic number usage - Ticket: 351201151, Magic: 2800, SL Points: 2800, Distance: 28.00000, Symbol: XAUUSD.iux
2025-09-12 09:30:33,241 [INFO] \U0001f522 #351201151: Magic=2800, SL Points=2800
2025-09-12 09:30:33,241 [DEBUG] #351201151: Entry=3633.00000, Current=3650.38000, SL=3605.00000, TP=3665.00000
2025-09-12 09:30:33,242 [DEBUG] #351201151: point_sl=28.00000, point_be=-3624.35000, is_moving_tp=True
2025-09-12 09:30:33,242 [DEBUG] #351201151: BUY Moving TP - Current: 3650.38000, Trigger: 8.65000
2025-09-12 09:30:33,242 [DEBUG] #351201151: BUY Moving TP factor -129.44107142857143 - New SL: -47.25000, New TP: 65.15000
2025-09-12 09:30:33,243 [DEBUG] #351201151: Modifying order - SL: 3605.00000 -> -47.25000, TP: 3665.00000 -> 65.15000
2025-09-12 09:30:33,262 [ERROR] #351201151 - ERROR in order modification: Order send failed: AutoTrading disabled by client
NoneType: None
2025-09-12 09:30:33,366 [INFO] \u274c #351201151 - ERROR in order modification: Order send failed: AutoTrading disabled by client
2025-09-12 09:30:33,367 [INFO] DEBUG: Order processing - Symbol: XAUUSD.iux, Group: , Total: 3, Processed: 3, Action: SL+TP
2025-09-12 09:30:33,368 [INFO] \U0001f4e6 SL+TP Processing: XAUUSD.iux (All) - 3/3 orders
2025-09-12 09:30:33,368 [DEBUG] Processed 3  positions for SL+TP
2025-09-12 09:31:03,421 [DEBUG] Processing auto BE/TP for 1 symbols: {'XAUUSD.iux'}
2025-09-12 09:31:03,421 [DEBUG] Processing 3 positions for XAUUSD.iux
2025-09-12 09:31:03,422 [DEBUG] Processing 3 positions for  (SL: True, TP: True)
2025-09-12 09:31:03,464 [DEBUG] DEBUG: Magic number usage - Ticket: 348075168, Magic: 2155, SL Points: 2155, Distance: 21.55000, Symbol: XAUUSD.iux
2025-09-12 09:31:03,464 [INFO] \U0001f522 #348075168: Magic=2155, SL Points=2155
2025-09-12 09:31:03,465 [DEBUG] #348075168: Entry=3593.70000, Current=3650.10000, SL=3594.20000, TP=3695.20000
2025-09-12 09:31:03,465 [DEBUG] #348075168: point_sl=21.55000, point_be=-3578.29800, is_moving_tp=True
2025-09-12 09:31:03,466 [DEBUG] #348075168: BUY Moving TP - Current: 3650.10000, Trigger: 15.40200
2025-09-12 09:31:03,466 [DEBUG] #348075168: BUY Moving TP factor -166.04631090487237 - New SL: -27.59800, New TP: 59.00200
2025-09-12 09:31:03,466 [DEBUG] #348075168: Modifying order - SL: 3594.20000 -> -27.59800, TP: 3695.20000 -> 59.00200
2025-09-12 09:31:03,486 [ERROR] #348075168 - ERROR in order modification: Order send failed: AutoTrading disabled by client
NoneType: None
2025-09-12 09:31:03,491 [INFO] \u274c #348075168 - ERROR in order modification: Order send failed: AutoTrading disabled by client
2025-09-12 09:31:03,513 [DEBUG] DEBUG: Magic number usage - Ticket: 351201149, Magic: 2800, SL Points: 2800, Distance: 28.00000, Symbol: XAUUSD.iux
2025-09-12 09:31:03,618 [INFO] \U0001f522 #351201149: Magic=2800, SL Points=2800
2025-09-12 09:31:03,618 [DEBUG] #351201149: Entry=3633.00000, Current=3650.10000, SL=3605.00000, TP=3655.00000
2025-09-12 09:31:03,619 [DEBUG] #351201149: point_sl=28.00000, point_be=-3624.45000, is_moving_tp=True
2025-09-12 09:31:03,619 [DEBUG] #351201149: BUY Moving TP - Current: 3650.10000, Trigger: 8.55000
2025-09-12 09:31:03,619 [DEBUG] #351201149: BUY Moving TP factor -129.44464285714287 - New SL: -47.35000, New TP: 65.05000
2025-09-12 09:31:03,619 [DEBUG] #351201149: Modifying order - SL: 3605.00000 -> -47.35000, TP: 3655.00000 -> 65.05000
2025-09-12 09:31:03,639 [ERROR] #351201149 - ERROR in order modification: Order send failed: AutoTrading disabled by client
NoneType: None
2025-09-12 09:31:03,751 [INFO] \u274c #351201149 - ERROR in order modification: Order send failed: AutoTrading disabled by client
2025-09-12 09:31:03,769 [DEBUG] DEBUG: Magic number usage - Ticket: 351201151, Magic: 2800, SL Points: 2800, Distance: 28.00000, Symbol: XAUUSD.iux
2025-09-12 09:31:03,772 [INFO] \U0001f522 #351201151: Magic=2800, SL Points=2800
2025-09-12 09:31:03,772 [DEBUG] #351201151: Entry=3633.00000, Current=3650.10000, SL=3605.00000, TP=3665.00000
2025-09-12 09:31:03,773 [DEBUG] #351201151: point_sl=28.00000, point_be=-3624.35000, is_moving_tp=True
2025-09-12 09:31:03,773 [DEBUG] #351201151: BUY Moving TP - Current: 3650.10000, Trigger: 8.65000
2025-09-12 09:31:03,773 [DEBUG] #351201151: BUY Moving TP factor -129.44107142857143 - New SL: -47.25000, New TP: 65.15000
2025-09-12 09:31:03,774 [DEBUG] #351201151: Modifying order - SL: 3605.00000 -> -47.25000, TP: 3665.00000 -> 65.15000
2025-09-12 09:31:03,794 [ERROR] #351201151 - ERROR in order modification: Order send failed: AutoTrading disabled by client
NoneType: None
2025-09-12 09:31:03,930 [INFO] \u274c #351201151 - ERROR in order modification: Order send failed: AutoTrading disabled by client
2025-09-12 09:31:03,931 [INFO] DEBUG: Order processing - Symbol: XAUUSD.iux, Group: , Total: 3, Processed: 3, Action: SL+TP
2025-09-12 09:31:03,932 [INFO] \U0001f4e6 SL+TP Processing: XAUUSD.iux (All) - 3/3 orders
2025-09-12 09:31:03,932 [DEBUG] Processed 3  positions for SL+TP
2025-09-12 09:31:33,995 [DEBUG] Processing auto BE/TP for 1 symbols: {'XAUUSD.iux'}
2025-09-12 09:31:33,995 [DEBUG] Processing 3 positions for XAUUSD.iux
2025-09-12 09:31:33,996 [DEBUG] Processing 3 positions for  (SL: True, TP: True)
2025-09-12 09:31:34,037 [DEBUG] DEBUG: Magic number usage - Ticket: 348075168, Magic: 2155, SL Points: 2155, Distance: 21.55000, Symbol: XAUUSD.iux
2025-09-12 09:31:34,038 [INFO] \U0001f522 #348075168: Magic=2155, SL Points=2155
2025-09-12 09:31:34,038 [DEBUG] #348075168: Entry=3593.70000, Current=3649.44000, SL=3594.20000, TP=3695.20000
2025-09-12 09:31:34,039 [DEBUG] #348075168: point_sl=21.55000, point_be=-3578.29800, is_moving_tp=True
2025-09-12 09:31:34,039 [DEBUG] #348075168: BUY Moving TP - Current: 3649.44000, Trigger: 15.40200
2025-09-12 09:31:34,039 [DEBUG] #348075168: BUY Moving TP factor -166.04631090487237 - New SL: -27.59800, New TP: 59.00200
2025-09-12 09:31:34,040 [DEBUG] #348075168: Modifying order - SL: 3594.20000 -> -27.59800, TP: 3695.20000 -> 59.00200
2025-09-12 09:31:34,059 [ERROR] #348075168 - ERROR in order modification: Order send failed: AutoTrading disabled by client
NoneType: None
2025-09-12 09:31:34,064 [INFO] \u274c #348075168 - ERROR in order modification: Order send failed: AutoTrading disabled by client
2025-09-12 09:31:34,082 [DEBUG] DEBUG: Magic number usage - Ticket: 351201149, Magic: 2800, SL Points: 2800, Distance: 28.00000, Symbol: XAUUSD.iux
2025-09-12 09:31:34,205 [INFO] \U0001f522 #351201149: Magic=2800, SL Points=2800
2025-09-12 09:31:34,206 [DEBUG] #351201149: Entry=3633.00000, Current=3649.44000, SL=3605.00000, TP=3655.00000
2025-09-12 09:31:34,206 [DEBUG] #351201149: point_sl=28.00000, point_be=-3624.45000, is_moving_tp=True
2025-09-12 09:31:34,206 [DEBUG] #351201149: BUY Moving TP - Current: 3649.44000, Trigger: 8.55000
2025-09-12 09:31:34,207 [DEBUG] #351201149: BUY Moving TP factor -129.44464285714287 - New SL: -47.35000, New TP: 65.05000
2025-09-12 09:31:34,207 [DEBUG] #351201149: Modifying order - SL: 3605.00000 -> -47.35000, TP: 3655.00000 -> 65.05000
2025-09-12 09:31:34,226 [ERROR] #351201149 - ERROR in order modification: Order send failed: AutoTrading disabled by client
NoneType: None
2025-09-12 09:31:34,360 [INFO] \u274c #351201149 - ERROR in order modification: Order send failed: AutoTrading disabled by client
2025-09-12 09:31:34,379 [DEBUG] DEBUG: Magic number usage - Ticket: 351201151, Magic: 2800, SL Points: 2800, Distance: 28.00000, Symbol: XAUUSD.iux
2025-09-12 09:31:34,380 [INFO] \U0001f522 #351201151: Magic=2800, SL Points=2800
2025-09-12 09:31:34,381 [DEBUG] #351201151: Entry=3633.00000, Current=3649.44000, SL=3605.00000, TP=3665.00000
2025-09-12 09:31:34,381 [DEBUG] #351201151: point_sl=28.00000, point_be=-3624.35000, is_moving_tp=True
2025-09-12 09:31:34,381 [DEBUG] #351201151: BUY Moving TP - Current: 3649.44000, Trigger: 8.65000
2025-09-12 09:31:34,382 [DEBUG] #351201151: BUY Moving TP factor -129.44107142857143 - New SL: -47.25000, New TP: 65.15000
2025-09-12 09:31:34,382 [DEBUG] #351201151: Modifying order - SL: 3605.00000 -> -47.25000, TP: 3665.00000 -> 65.15000
2025-09-12 09:31:34,401 [ERROR] #351201151 - ERROR in order modification: Order send failed: AutoTrading disabled by client
NoneType: None
2025-09-12 09:31:34,532 [INFO] \u274c #351201151 - ERROR in order modification: Order send failed: AutoTrading disabled by client
2025-09-12 09:31:34,533 [INFO] DEBUG: Order processing - Symbol: XAUUSD.iux, Group: , Total: 3, Processed: 3, Action: SL+TP
2025-09-12 09:31:34,534 [INFO] \U0001f4e6 SL+TP Processing: XAUUSD.iux (All) - 3/3 orders
2025-09-12 09:31:34,534 [DEBUG] Processed 3  positions for SL+TP
2025-09-12 09:32:04,596 [DEBUG] Processing auto BE/TP for 1 symbols: {'XAUUSD.iux'}
2025-09-12 09:32:04,596 [DEBUG] Processing 3 positions for XAUUSD.iux
2025-09-12 09:32:04,597 [DEBUG] Processing 3 positions for  (SL: True, TP: True)
2025-09-12 09:32:04,633 [DEBUG] DEBUG: Magic number usage - Ticket: 348075168, Magic: 2155, SL Points: 2155, Distance: 21.55000, Symbol: XAUUSD.iux
2025-09-12 09:32:04,634 [INFO] \U0001f522 #348075168: Magic=2155, SL Points=2155
2025-09-12 09:32:04,635 [DEBUG] #348075168: Entry=3593.70000, Current=3648.68000, SL=3594.20000, TP=3695.20000
2025-09-12 09:32:04,635 [DEBUG] #348075168: point_sl=21.55000, point_be=-3578.29800, is_moving_tp=True
2025-09-12 09:32:04,635 [DEBUG] #348075168: BUY Moving TP - Current: 3648.68000, Trigger: 15.40200
2025-09-12 09:32:04,636 [DEBUG] #348075168: BUY Moving TP factor -166.04631090487237 - New SL: -27.59800, New TP: 59.00200
2025-09-12 09:32:04,636 [DEBUG] #348075168: Modifying order - SL: 3594.20000 -> -27.59800, TP: 3695.20000 -> 59.00200
2025-09-12 09:32:04,657 [ERROR] #348075168 - ERROR in order modification: Order send failed: AutoTrading disabled by client
NoneType: None
2025-09-12 09:32:04,662 [INFO] \u274c #348075168 - ERROR in order modification: Order send failed: AutoTrading disabled by client
2025-09-12 09:32:04,682 [DEBUG] DEBUG: Magic number usage - Ticket: 351201149, Magic: 2800, SL Points: 2800, Distance: 28.00000, Symbol: XAUUSD.iux
2025-09-12 09:32:04,789 [INFO] \U0001f522 #351201149: Magic=2800, SL Points=2800
2025-09-12 09:32:04,790 [DEBUG] #351201149: Entry=3633.00000, Current=3648.68000, SL=3605.00000, TP=3655.00000
2025-09-12 09:32:04,790 [DEBUG] #351201149: point_sl=28.00000, point_be=-3624.45000, is_moving_tp=True
2025-09-12 09:32:04,790 [DEBUG] #351201149: BUY Moving TP - Current: 3648.68000, Trigger: 8.55000
2025-09-12 09:32:04,790 [DEBUG] #351201149: BUY Moving TP factor -129.44464285714287 - New SL: -47.35000, New TP: 65.05000
2025-09-12 09:32:04,791 [DEBUG] #351201149: Modifying order - SL: 3605.00000 -> -47.35000, TP: 3655.00000 -> 65.05000
2025-09-12 09:32:04,808 [ERROR] #351201149 - ERROR in order modification: Order send failed: AutoTrading disabled by client
NoneType: None
2025-09-12 09:32:04,927 [INFO] \u274c #351201149 - ERROR in order modification: Order send failed: AutoTrading disabled by client
2025-09-12 09:32:04,945 [DEBUG] DEBUG: Magic number usage - Ticket: 351201151, Magic: 2800, SL Points: 2800, Distance: 28.00000, Symbol: XAUUSD.iux
2025-09-12 09:32:04,946 [INFO] \U0001f522 #351201151: Magic=2800, SL Points=2800
2025-09-12 09:32:04,947 [DEBUG] #351201151: Entry=3633.00000, Current=3648.68000, SL=3605.00000, TP=3665.00000
2025-09-12 09:32:04,947 [DEBUG] #351201151: point_sl=28.00000, point_be=-3624.35000, is_moving_tp=True
2025-09-12 09:32:04,947 [DEBUG] #351201151: BUY Moving TP - Current: 3648.68000, Trigger: 8.65000
2025-09-12 09:32:04,948 [DEBUG] #351201151: BUY Moving TP factor -129.44107142857143 - New SL: -47.25000, New TP: 65.15000
2025-09-12 09:32:04,948 [DEBUG] #351201151: Modifying order - SL: 3605.00000 -> -47.25000, TP: 3665.00000 -> 65.15000
2025-09-12 09:32:04,968 [ERROR] #351201151 - ERROR in order modification: Order send failed: AutoTrading disabled by client
NoneType: None
2025-09-12 09:32:05,082 [INFO] \u274c #351201151 - ERROR in order modification: Order send failed: AutoTrading disabled by client
2025-09-12 09:32:05,082 [INFO] DEBUG: Order processing - Symbol: XAUUSD.iux, Group: , Total: 3, Processed: 3, Action: SL+TP
2025-09-12 09:32:05,084 [INFO] \U0001f4e6 SL+TP Processing: XAUUSD.iux (All) - 3/3 orders
2025-09-12 09:32:05,085 [DEBUG] Processed 3  positions for SL+TP
2025-09-12 09:32:35,148 [DEBUG] Processing auto BE/TP for 1 symbols: {'XAUUSD.iux'}
2025-09-12 09:32:35,149 [DEBUG] Processing 3 positions for XAUUSD.iux
2025-09-12 09:32:35,149 [DEBUG] Processing 3 positions for  (SL: True, TP: True)
2025-09-12 09:32:35,189 [DEBUG] DEBUG: Magic number usage - Ticket: 348075168, Magic: 2155, SL Points: 2155, Distance: 21.55000, Symbol: XAUUSD.iux
2025-09-12 09:32:35,190 [INFO] \U0001f522 #348075168: Magic=2155, SL Points=2155
2025-09-12 09:32:35,190 [DEBUG] #348075168: Entry=3593.70000, Current=3648.81000, SL=3594.20000, TP=3695.20000
2025-09-12 09:32:35,191 [DEBUG] #348075168: point_sl=21.55000, point_be=-3578.29800, is_moving_tp=True
2025-09-12 09:32:35,191 [DEBUG] #348075168: BUY Moving TP - Current: 3648.81000, Trigger: 15.40200
2025-09-12 09:32:35,192 [DEBUG] #348075168: BUY Moving TP factor -166.04631090487237 - New SL: -27.59800, New TP: 59.00200
2025-09-12 09:32:35,192 [DEBUG] #348075168: Modifying order - SL: 3594.20000 -> -27.59800, TP: 3695.20000 -> 59.00200
2025-09-12 09:32:35,211 [ERROR] #348075168 - ERROR in order modification: Order send failed: AutoTrading disabled by client
NoneType: None
2025-09-12 09:32:35,217 [INFO] \u274c #348075168 - ERROR in order modification: Order send failed: AutoTrading disabled by client
2025-09-12 09:32:35,238 [DEBUG] DEBUG: Magic number usage - Ticket: 351201149, Magic: 2800, SL Points: 2800, Distance: 28.00000, Symbol: XAUUSD.iux
2025-09-12 09:32:35,328 [INFO] \U0001f522 #351201149: Magic=2800, SL Points=2800
2025-09-12 09:32:35,329 [DEBUG] #351201149: Entry=3633.00000, Current=3648.81000, SL=3605.00000, TP=3655.00000
2025-09-12 09:32:35,329 [DEBUG] #351201149: point_sl=28.00000, point_be=-3624.45000, is_moving_tp=True
2025-09-12 09:32:35,329 [DEBUG] #351201149: BUY Moving TP - Current: 3648.81000, Trigger: 8.55000
2025-09-12 09:32:35,330 [DEBUG] #351201149: BUY Moving TP factor -129.44464285714287 - New SL: -47.35000, New TP: 65.05000
2025-09-12 09:32:35,330 [DEBUG] #351201149: Modifying order - SL: 3605.00000 -> -47.35000, TP: 3655.00000 -> 65.05000
2025-09-12 09:32:35,348 [ERROR] #351201149 - ERROR in order modification: Order send failed: AutoTrading disabled by client
NoneType: None
2025-09-12 09:32:35,453 [INFO] \u274c #351201149 - ERROR in order modification: Order send failed: AutoTrading disabled by client
2025-09-12 09:32:35,473 [DEBUG] DEBUG: Magic number usage - Ticket: 351201151, Magic: 2800, SL Points: 2800, Distance: 28.00000, Symbol: XAUUSD.iux
2025-09-12 09:32:35,474 [INFO] \U0001f522 #351201151: Magic=2800, SL Points=2800
2025-09-12 09:32:35,475 [DEBUG] #351201151: Entry=3633.00000, Current=3648.81000, SL=3605.00000, TP=3665.00000
2025-09-12 09:32:35,475 [DEBUG] #351201151: point_sl=28.00000, point_be=-3624.35000, is_moving_tp=True
2025-09-12 09:32:35,475 [DEBUG] #351201151: BUY Moving TP - Current: 3648.81000, Trigger: 8.65000
2025-09-12 09:32:35,475 [DEBUG] #351201151: BUY Moving TP factor -129.44107142857143 - New SL: -47.25000, New TP: 65.15000
2025-09-12 09:32:35,476 [DEBUG] #351201151: Modifying order - SL: 3605.00000 -> -47.25000, TP: 3665.00000 -> 65.15000
2025-09-12 09:32:35,494 [ERROR] #351201151 - ERROR in order modification: Order send failed: AutoTrading disabled by client
NoneType: None
2025-09-12 09:32:35,621 [INFO] \u274c #351201151 - ERROR in order modification: Order send failed: AutoTrading disabled by client
2025-09-12 09:32:35,621 [INFO] DEBUG: Order processing - Symbol: XAUUSD.iux, Group: , Total: 3, Processed: 3, Action: SL+TP
2025-09-12 09:32:35,622 [INFO] \U0001f4e6 SL+TP Processing: XAUUSD.iux (All) - 3/3 orders
2025-09-12 09:32:35,623 [DEBUG] Processed 3  positions for SL+TP
2025-09-12 09:33:05,684 [DEBUG] Processing auto BE/TP for 1 symbols: {'XAUUSD.iux'}
2025-09-12 09:33:05,684 [DEBUG] Processing 3 positions for XAUUSD.iux
2025-09-12 09:33:05,685 [DEBUG] Processing 3 positions for  (SL: True, TP: True)
2025-09-12 09:33:05,725 [DEBUG] DEBUG: Magic number usage - Ticket: 348075168, Magic: 2155, SL Points: 2155, Distance: 21.55000, Symbol: XAUUSD.iux
2025-09-12 09:33:05,726 [INFO] \U0001f522 #348075168: Magic=2155, SL Points=2155
2025-09-12 09:33:05,726 [DEBUG] #348075168: Entry=3593.70000, Current=3649.39000, SL=3594.20000, TP=3695.20000
2025-09-12 09:33:05,727 [DEBUG] #348075168: point_sl=21.55000, point_be=-3578.29800, is_moving_tp=True
2025-09-12 09:33:05,727 [DEBUG] #348075168: BUY Moving TP - Current: 3649.39000, Trigger: 15.40200
2025-09-12 09:33:05,728 [DEBUG] #348075168: BUY Moving TP factor -166.04631090487237 - New SL: -27.59800, New TP: 59.00200
2025-09-12 09:33:05,728 [DEBUG] #348075168: Modifying order - SL: 3594.20000 -> -27.59800, TP: 3695.20000 -> 59.00200
2025-09-12 09:33:05,746 [ERROR] #348075168 - ERROR in order modification: Order send failed: AutoTrading disabled by client
NoneType: None
2025-09-12 09:33:05,752 [INFO] \u274c #348075168 - ERROR in order modification: Order send failed: AutoTrading disabled by client
2025-09-12 09:33:05,772 [DEBUG] DEBUG: Magic number usage - Ticket: 351201149, Magic: 2800, SL Points: 2800, Distance: 28.00000, Symbol: XAUUSD.iux
2025-09-12 09:33:05,861 [INFO] \U0001f522 #351201149: Magic=2800, SL Points=2800
2025-09-12 09:33:05,862 [DEBUG] #351201149: Entry=3633.00000, Current=3649.39000, SL=3605.00000, TP=3655.00000
2025-09-12 09:33:05,862 [DEBUG] #351201149: point_sl=28.00000, point_be=-3624.45000, is_moving_tp=True
2025-09-12 09:33:05,862 [DEBUG] #351201149: BUY Moving TP - Current: 3649.39000, Trigger: 8.55000
2025-09-12 09:33:05,863 [DEBUG] #351201149: BUY Moving TP factor -129.44464285714287 - New SL: -47.35000, New TP: 65.05000
2025-09-12 09:33:05,863 [DEBUG] #351201149: Modifying order - SL: 3605.00000 -> -47.35000, TP: 3655.00000 -> 65.05000
2025-09-12 09:33:05,882 [ERROR] #351201149 - ERROR in order modification: Order send failed: AutoTrading disabled by client
NoneType: None
2025-09-12 09:33:06,018 [INFO] \u274c #351201149 - ERROR in order modification: Order send failed: AutoTrading disabled by client
2025-09-12 09:33:06,037 [DEBUG] DEBUG: Magic number usage - Ticket: 351201151, Magic: 2800, SL Points: 2800, Distance: 28.00000, Symbol: XAUUSD.iux
2025-09-12 09:33:06,143 [INFO] \U0001f522 #351201151: Magic=2800, SL Points=2800
2025-09-12 09:33:06,144 [DEBUG] #351201151: Entry=3633.00000, Current=3649.39000, SL=3605.00000, TP=3665.00000
2025-09-12 09:33:06,144 [DEBUG] #351201151: point_sl=28.00000, point_be=-3624.35000, is_moving_tp=True
2025-09-12 09:33:06,144 [DEBUG] #351201151: BUY Moving TP - Current: 3649.39000, Trigger: 8.65000
2025-09-12 09:33:06,145 [DEBUG] #351201151: BUY Moving TP factor -129.44107142857143 - New SL: -47.25000, New TP: 65.15000
2025-09-12 09:33:06,145 [DEBUG] #351201151: Modifying order - SL: 3605.00000 -> -47.25000, TP: 3665.00000 -> 65.15000
2025-09-12 09:33:06,164 [ERROR] #351201151 - ERROR in order modification: Order send failed: AutoTrading disabled by client
NoneType: None
2025-09-12 09:33:06,284 [INFO] \u274c #351201151 - ERROR in order modification: Order send failed: AutoTrading disabled by client
2025-09-12 09:33:06,284 [INFO] DEBUG: Order processing - Symbol: XAUUSD.iux, Group: , Total: 3, Processed: 3, Action: SL+TP
2025-09-12 09:33:06,285 [INFO] \U0001f4e6 SL+TP Processing: XAUUSD.iux (All) - 3/3 orders
2025-09-12 09:33:06,286 [DEBUG] Processed 3  positions for SL+TP
2025-09-12 09:33:36,353 [DEBUG] Processing auto BE/TP for 1 symbols: {'XAUUSD.iux'}
2025-09-12 09:33:36,353 [DEBUG] Processing 3 positions for XAUUSD.iux
2025-09-12 09:33:36,354 [DEBUG] Processing 3 positions for  (SL: True, TP: True)
2025-09-12 09:33:36,397 [DEBUG] DEBUG: Magic number usage - Ticket: 348075168, Magic: 2155, SL Points: 2155, Distance: 21.55000, Symbol: XAUUSD.iux
2025-09-12 09:33:36,397 [INFO] \U0001f522 #348075168: Magic=2155, SL Points=2155
2025-09-12 09:33:36,398 [DEBUG] #348075168: Entry=3593.70000, Current=3649.06000, SL=3594.20000, TP=3695.20000
2025-09-12 09:33:36,398 [DEBUG] #348075168: point_sl=21.55000, point_be=-3578.29800, is_moving_tp=True
2025-09-12 09:33:36,398 [DEBUG] #348075168: BUY Moving TP - Current: 3649.06000, Trigger: 15.40200
2025-09-12 09:33:36,399 [DEBUG] #348075168: BUY Moving TP factor -166.04631090487237 - New SL: -27.59800, New TP: 59.00200
2025-09-12 09:33:36,399 [DEBUG] #348075168: Modifying order - SL: 3594.20000 -> -27.59800, TP: 3695.20000 -> 59.00200
2025-09-12 09:33:36,418 [ERROR] #348075168 - ERROR in order modification: Order send failed: AutoTrading disabled by client
NoneType: None
2025-09-12 09:33:36,523 [INFO] \u274c #348075168 - ERROR in order modification: Order send failed: AutoTrading disabled by client
2025-09-12 09:33:36,543 [DEBUG] DEBUG: Magic number usage - Ticket: 351201149, Magic: 2800, SL Points: 2800, Distance: 28.00000, Symbol: XAUUSD.iux
2025-09-12 09:33:36,544 [INFO] \U0001f522 #351201149: Magic=2800, SL Points=2800
2025-09-12 09:33:36,545 [DEBUG] #351201149: Entry=3633.00000, Current=3649.06000, SL=3605.00000, TP=3655.00000
2025-09-12 09:33:36,545 [DEBUG] #351201149: point_sl=28.00000, point_be=-3624.45000, is_moving_tp=True
2025-09-12 09:33:36,545 [DEBUG] #351201149: BUY Moving TP - Current: 3649.06000, Trigger: 8.55000
2025-09-12 09:33:36,546 [DEBUG] #351201149: BUY Moving TP factor -129.44464285714287 - New SL: -47.35000, New TP: 65.05000
2025-09-12 09:33:36,546 [DEBUG] #351201149: Modifying order - SL: 3605.00000 -> -47.35000, TP: 3655.00000 -> 65.05000
2025-09-12 09:33:36,565 [ERROR] #351201149 - ERROR in order modification: Order send failed: AutoTrading disabled by client
NoneType: None
2025-09-12 09:33:36,597 [INFO] \u274c #351201149 - ERROR in order modification: Order send failed: AutoTrading disabled by client
2025-09-12 09:33:36,617 [DEBUG] DEBUG: Magic number usage - Ticket: 351201151, Magic: 2800, SL Points: 2800, Distance: 28.00000, Symbol: XAUUSD.iux
2025-09-12 09:33:36,619 [INFO] \U0001f522 #351201151: Magic=2800, SL Points=2800
2025-09-12 09:33:36,620 [DEBUG] #351201151: Entry=3633.00000, Current=3649.06000, SL=3605.00000, TP=3665.00000
2025-09-12 09:33:36,620 [DEBUG] #351201151: point_sl=28.00000, point_be=-3624.35000, is_moving_tp=True
2025-09-12 09:33:36,620 [DEBUG] #351201151: BUY Moving TP - Current: 3649.06000, Trigger: 8.65000
2025-09-12 09:33:36,621 [DEBUG] #351201151: BUY Moving TP factor -129.44107142857143 - New SL: -47.25000, New TP: 65.15000
2025-09-12 09:33:36,621 [DEBUG] #351201151: Modifying order - SL: 3605.00000 -> -47.25000, TP: 3665.00000 -> 65.15000
2025-09-12 09:33:36,640 [ERROR] #351201151 - ERROR in order modification: Order send failed: AutoTrading disabled by client
NoneType: None
2025-09-12 09:33:36,752 [INFO] \u274c #351201151 - ERROR in order modification: Order send failed: AutoTrading disabled by client
2025-09-12 09:33:36,752 [INFO] DEBUG: Order processing - Symbol: XAUUSD.iux, Group: , Total: 3, Processed: 3, Action: SL+TP
2025-09-12 09:33:36,753 [INFO] \U0001f4e6 SL+TP Processing: XAUUSD.iux (All) - 3/3 orders
2025-09-12 09:33:36,753 [DEBUG] Processed 3  positions for SL+TP
2025-09-12 09:34:06,820 [DEBUG] Processing auto BE/TP for 1 symbols: {'XAUUSD.iux'}
2025-09-12 09:34:06,820 [DEBUG] Processing 3 positions for XAUUSD.iux
2025-09-12 09:34:06,821 [DEBUG] Processing 3 positions for  (SL: True, TP: True)
2025-09-12 09:34:06,862 [DEBUG] DEBUG: Magic number usage - Ticket: 348075168, Magic: 2155, SL Points: 2155, Distance: 21.55000, Symbol: XAUUSD.iux
2025-09-12 09:34:06,863 [INFO] \U0001f522 #348075168: Magic=2155, SL Points=2155
2025-09-12 09:34:06,863 [DEBUG] #348075168: Entry=3593.70000, Current=3649.77000, SL=3594.20000, TP=3695.20000
2025-09-12 09:34:06,864 [DEBUG] #348075168: point_sl=21.55000, point_be=-3578.29800, is_moving_tp=True
2025-09-12 09:34:06,864 [DEBUG] #348075168: BUY Moving TP - Current: 3649.77000, Trigger: 15.40200
2025-09-12 09:34:06,865 [DEBUG] #348075168: BUY Moving TP factor -166.04631090487237 - New SL: -27.59800, New TP: 59.00200
2025-09-12 09:34:06,865 [DEBUG] #348075168: Modifying order - SL: 3594.20000 -> -27.59800, TP: 3695.20000 -> 59.00200
2025-09-12 09:34:06,885 [ERROR] #348075168 - ERROR in order modification: Order send failed: AutoTrading disabled by client
NoneType: None
2025-09-12 09:34:06,890 [INFO] \u274c #348075168 - ERROR in order modification: Order send failed: AutoTrading disabled by client
2025-09-12 09:34:06,909 [DEBUG] DEBUG: Magic number usage - Ticket: 351201149, Magic: 2800, SL Points: 2800, Distance: 28.00000, Symbol: XAUUSD.iux
2025-09-12 09:34:07,019 [INFO] \U0001f522 #351201149: Magic=2800, SL Points=2800
2025-09-12 09:34:07,020 [DEBUG] #351201149: Entry=3633.00000, Current=3649.77000, SL=3605.00000, TP=3655.00000
2025-09-12 09:34:07,020 [DEBUG] #351201149: point_sl=28.00000, point_be=-3624.45000, is_moving_tp=True
2025-09-12 09:34:07,020 [DEBUG] #351201149: BUY Moving TP - Current: 3649.77000, Trigger: 8.55000
2025-09-12 09:34:07,020 [DEBUG] #351201149: BUY Moving TP factor -129.44464285714287 - New SL: -47.35000, New TP: 65.05000
2025-09-12 09:34:07,021 [DEBUG] #351201149: Modifying order - SL: 3605.00000 -> -47.35000, TP: 3655.00000 -> 65.05000
2025-09-12 09:34:07,040 [ERROR] #351201149 - ERROR in order modification: Order send failed: AutoTrading disabled by client
NoneType: None
2025-09-12 09:34:07,189 [INFO] \u274c #351201149 - ERROR in order modification: Order send failed: AutoTrading disabled by client
2025-09-12 09:34:07,212 [DEBUG] DEBUG: Magic number usage - Ticket: 351201151, Magic: 2800, SL Points: 2800, Distance: 28.00000, Symbol: XAUUSD.iux
2025-09-12 09:34:07,214 [INFO] \U0001f522 #351201151: Magic=2800, SL Points=2800
2025-09-12 09:34:07,214 [DEBUG] #351201151: Entry=3633.00000, Current=3649.77000, SL=3605.00000, TP=3665.00000
2025-09-12 09:34:07,214 [DEBUG] #351201151: point_sl=28.00000, point_be=-3624.35000, is_moving_tp=True
2025-09-12 09:34:07,215 [DEBUG] #351201151: BUY Moving TP - Current: 3649.77000, Trigger: 8.65000
2025-09-12 09:34:07,215 [DEBUG] #351201151: BUY Moving TP factor -129.44107142857143 - New SL: -47.25000, New TP: 65.15000
2025-09-12 09:34:07,215 [DEBUG] #351201151: Modifying order - SL: 3605.00000 -> -47.25000, TP: 3665.00000 -> 65.15000
2025-09-12 09:34:07,239 [ERROR] #351201151 - ERROR in order modification: Order send failed: AutoTrading disabled by client
NoneType: None
2025-09-12 09:34:07,388 [INFO] \u274c #351201151 - ERROR in order modification: Order send failed: AutoTrading disabled by client
2025-09-12 09:34:07,389 [INFO] DEBUG: Order processing - Symbol: XAUUSD.iux, Group: , Total: 3, Processed: 3, Action: SL+TP
2025-09-12 09:34:07,390 [INFO] \U0001f4e6 SL+TP Processing: XAUUSD.iux (All) - 3/3 orders
2025-09-12 09:34:07,390 [DEBUG] Processed 3  positions for SL+TP
2025-09-12 09:34:37,451 [DEBUG] Processing auto BE/TP for 1 symbols: {'XAUUSD.iux'}
2025-09-12 09:34:37,452 [DEBUG] Processing 3 positions for XAUUSD.iux
2025-09-12 09:34:37,452 [DEBUG] Processing 3 positions for  (SL: True, TP: True)
2025-09-12 09:34:37,496 [DEBUG] DEBUG: Magic number usage - Ticket: 348075168, Magic: 2155, SL Points: 2155, Distance: 21.55000, Symbol: XAUUSD.iux
2025-09-12 09:34:37,497 [INFO] \U0001f522 #348075168: Magic=2155, SL Points=2155
2025-09-12 09:34:37,498 [DEBUG] #348075168: Entry=3593.70000, Current=3649.51000, SL=3594.20000, TP=3695.20000
2025-09-12 09:34:37,498 [DEBUG] #348075168: point_sl=21.55000, point_be=-3578.29800, is_moving_tp=True
2025-09-12 09:34:37,498 [DEBUG] #348075168: BUY Moving TP - Current: 3649.51000, Trigger: 15.40200
2025-09-12 09:34:37,499 [DEBUG] #348075168: BUY Moving TP factor -166.04631090487237 - New SL: -27.59800, New TP: 59.00200
2025-09-12 09:34:37,499 [DEBUG] #348075168: Modifying order - SL: 3594.20000 -> -27.59800, TP: 3695.20000 -> 59.00200
2025-09-12 09:34:37,518 [ERROR] #348075168 - ERROR in order modification: Order send failed: AutoTrading disabled by client
NoneType: None
2025-09-12 09:34:37,524 [INFO] \u274c #348075168 - ERROR in order modification: Order send failed: AutoTrading disabled by client
2025-09-12 09:34:37,545 [DEBUG] DEBUG: Magic number usage - Ticket: 351201149, Magic: 2800, SL Points: 2800, Distance: 28.00000, Symbol: XAUUSD.iux
2025-09-12 09:34:37,675 [INFO] \U0001f522 #351201149: Magic=2800, SL Points=2800
2025-09-12 09:34:37,677 [DEBUG] #351201149: Entry=3633.00000, Current=3649.51000, SL=3605.00000, TP=3655.00000
2025-09-12 09:34:37,677 [DEBUG] #351201149: point_sl=28.00000, point_be=-3624.45000, is_moving_tp=True
2025-09-12 09:34:37,677 [DEBUG] #351201149: BUY Moving TP - Current: 3649.51000, Trigger: 8.55000
2025-09-12 09:34:37,678 [DEBUG] #351201149: BUY Moving TP factor -129.44464285714287 - New SL: -47.35000, New TP: 65.05000
2025-09-12 09:34:37,678 [DEBUG] #351201149: Modifying order - SL: 3605.00000 -> -47.35000, TP: 3655.00000 -> 65.05000
2025-09-12 09:34:37,698 [ERROR] #351201149 - ERROR in order modification: Order send failed: AutoTrading disabled by client
NoneType: None
2025-09-12 09:34:37,807 [INFO] \u274c #351201149 - ERROR in order modification: Order send failed: AutoTrading disabled by client
2025-09-12 09:34:37,831 [DEBUG] DEBUG: Magic number usage - Ticket: 351201151, Magic: 2800, SL Points: 2800, Distance: 28.00000, Symbol: XAUUSD.iux
2025-09-12 09:34:37,832 [INFO] \U0001f522 #351201151: Magic=2800, SL Points=2800
2025-09-12 09:34:37,833 [DEBUG] #351201151: Entry=3633.00000, Current=3649.51000, SL=3605.00000, TP=3665.00000
2025-09-12 09:34:37,833 [DEBUG] #351201151: point_sl=28.00000, point_be=-3624.35000, is_moving_tp=True
2025-09-12 09:34:37,833 [DEBUG] #351201151: BUY Moving TP - Current: 3649.51000, Trigger: 8.65000
2025-09-12 09:34:37,834 [DEBUG] #351201151: BUY Moving TP factor -129.44107142857143 - New SL: -47.25000, New TP: 65.15000
2025-09-12 09:34:37,834 [DEBUG] #351201151: Modifying order - SL: 3605.00000 -> -47.25000, TP: 3665.00000 -> 65.15000
2025-09-12 09:34:37,855 [ERROR] #351201151 - ERROR in order modification: Order send failed: AutoTrading disabled by client
NoneType: None
2025-09-12 09:34:37,963 [INFO] \u274c #351201151 - ERROR in order modification: Order send failed: AutoTrading disabled by client
2025-09-12 09:34:37,964 [INFO] DEBUG: Order processing - Symbol: XAUUSD.iux, Group: , Total: 3, Processed: 3, Action: SL+TP
2025-09-12 09:34:37,965 [INFO] \U0001f4e6 SL+TP Processing: XAUUSD.iux (All) - 3/3 orders
2025-09-12 09:34:37,965 [DEBUG] Processed 3  positions for SL+TP
2025-09-12 09:34:46,601 [INFO] \U0001f534 Webhook server STOPPED
2025-09-12 09:34:46,601 [INFO] \U0001f534 Webhook server stopped
2025-09-12 09:34:46,601 [INFO] \U0001f534 Stopping background processes...
2025-09-12 09:34:48,604 [INFO] \U0001f534 Stopping AI bot scheduler...
2025-09-12 09:34:50,609 [INFO] \U0001f534 MT5 connection closed
2025-09-12 09:35:13,926 [INFO] Serving on http://0.0.0.0:5050
2025-09-12 09:35:14,424 [INFO] \U0001f7e2 Webhook server STARTED on port 5050
2025-09-12 09:35:14,928 [INFO] \U0001f7e2 Orders processing loop started
2025-09-12 09:35:14,962 [INFO] \U0001f7e2 Orders processing loop started
2025-09-12 09:35:14,962 [DEBUG] Processing auto BE/TP for 1 symbols: {'XAUUSD.iux'}
2025-09-12 09:35:14,963 [DEBUG] Processing 3 positions for XAUUSD.iux
2025-09-12 09:35:14,963 [DEBUG] Processing 3 positions for  (SL: True, TP: True)
2025-09-12 09:35:14,999 [DEBUG] DEBUG: Magic number usage - Ticket: 348075168, Magic: 2155, SL Points: 2155, Distance: 21.55000, Symbol: XAUUSD.iux
2025-09-12 09:35:14,999 [INFO] \U0001f4cb TabOrders initialized - Auto BE enabled, auto refresh disabled
2025-09-12 09:35:16,183 [INFO] \U0001f522 #348075168: Magic=2155, SL Points=2155
2025-09-12 09:35:16,184 [DEBUG] #348075168: Entry=3593.70000, Current=3649.83000, SL=3594.20000, TP=3695.20000
2025-09-12 09:35:16,185 [DEBUG] #348075168: point_sl=21.55000, point_be=-3578.29800, is_moving_tp=True
2025-09-12 09:35:16,185 [DEBUG] #348075168: BUY Moving TP - Current: 3649.83000, Trigger: 15.40200
2025-09-12 09:35:16,187 [DEBUG] #348075168: BUY Moving TP factor -166.04631090487237 - New SL: -27.59800, New TP: 59.00200
2025-09-12 09:35:16,187 [DEBUG] #348075168: Modifying order - SL: 3594.20000 -> -27.59800, TP: 3695.20000 -> 59.00200
2025-09-12 09:35:16,205 [ERROR] #348075168 - ERROR in order modification: Order send failed: AutoTrading disabled by client
NoneType: None
2025-09-12 09:35:16,251 [INFO] \u274c #348075168 - ERROR in order modification: Order send failed: AutoTrading disabled by client
2025-09-12 09:35:16,270 [DEBUG] DEBUG: Magic number usage - Ticket: 351201149, Magic: 2800, SL Points: 2800, Distance: 28.00000, Symbol: XAUUSD.iux
2025-09-12 09:35:16,273 [INFO] \U0001f522 #351201149: Magic=2800, SL Points=2800
2025-09-12 09:35:16,274 [DEBUG] #351201149: Entry=3633.00000, Current=3649.83000, SL=3605.00000, TP=3655.00000
2025-09-12 09:35:16,274 [DEBUG] #351201149: point_sl=28.00000, point_be=-3624.45000, is_moving_tp=True
2025-09-12 09:35:16,275 [DEBUG] #351201149: BUY Moving TP - Current: 3649.83000, Trigger: 8.55000
2025-09-12 09:35:16,275 [DEBUG] #351201149: BUY Moving TP factor -129.44464285714287 - New SL: -47.35000, New TP: 65.05000
2025-09-12 09:35:16,276 [DEBUG] #351201149: Modifying order - SL: 3605.00000 -> -47.35000, TP: 3655.00000 -> 65.05000
2025-09-12 09:35:16,294 [ERROR] #351201149 - ERROR in order modification: Order send failed: AutoTrading disabled by client
NoneType: None
2025-09-12 09:35:16,300 [INFO] \u274c #351201149 - ERROR in order modification: Order send failed: AutoTrading disabled by client
2025-09-12 09:35:16,319 [DEBUG] DEBUG: Magic number usage - Ticket: 351201151, Magic: 2800, SL Points: 2800, Distance: 28.00000, Symbol: XAUUSD.iux
2025-09-12 09:35:16,321 [INFO] \U0001f522 #351201151: Magic=2800, SL Points=2800
2025-09-12 09:35:16,321 [DEBUG] #351201151: Entry=3633.00000, Current=3649.83000, SL=3605.00000, TP=3665.00000
2025-09-12 09:35:16,322 [DEBUG] #351201151: point_sl=28.00000, point_be=-3624.35000, is_moving_tp=True
2025-09-12 09:35:16,322 [DEBUG] #351201151: BUY Moving TP - Current: 3649.83000, Trigger: 8.65000
2025-09-12 09:35:16,323 [DEBUG] #351201151: BUY Moving TP factor -129.44107142857143 - New SL: -47.25000, New TP: 65.15000
2025-09-12 09:35:16,323 [DEBUG] #351201151: Modifying order - SL: 3605.00000 -> -47.25000, TP: 3665.00000 -> 65.15000
2025-09-12 09:35:16,344 [ERROR] #351201151 - ERROR in order modification: Order send failed: AutoTrading disabled by client
NoneType: None
2025-09-12 09:35:16,348 [INFO] \u274c #351201151 - ERROR in order modification: Order send failed: AutoTrading disabled by client
2025-09-12 09:35:16,349 [INFO] DEBUG: Order processing - Symbol: XAUUSD.iux, Group: , Total: 3, Processed: 3, Action: SL+TP
2025-09-12 09:35:16,350 [INFO] \U0001f4e6 SL+TP Processing: XAUUSD.iux (All) - 3/3 orders
2025-09-12 09:35:16,350 [DEBUG] Processed 3  positions for SL+TP
2025-09-12 09:35:21,999 [INFO] \U0001f534 Webhook server STOPPED
2025-09-12 09:35:21,999 [INFO] \U0001f534 Webhook server stopped
2025-09-12 09:35:22,000 [INFO] \U0001f534 Stopping background processes...
2025-09-12 09:35:24,000 [INFO] \U0001f534 Stopping AI bot scheduler...
2025-09-12 09:35:26,014 [INFO] \U0001f534 MT5 connection closed
