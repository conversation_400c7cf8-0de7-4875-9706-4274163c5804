2025-10-20 16:30:14,820 [INFO] Serving on http://0.0.0.0:5050
2025-10-20 16:30:15,317 [INFO] \U0001f7e2 Webhook server STARTED on port 5050
2025-10-20 16:30:15,619 [INFO] \U0001f4cb TabOrders initialized - All auto functions disabled
2025-10-20 16:30:36,804 [INFO] \U0001f534 Webhook server STOPPED
2025-10-20 16:30:36,804 [INFO] \U0001f534 Webhook server stopped
2025-10-20 16:30:36,804 [INFO] \U0001f534 Stopping background processes...
2025-10-20 16:30:38,808 [INFO] \U0001f534 MT5 connection closed
2025-10-20 16:30:41,564 [INFO] Serving on http://0.0.0.0:5050
2025-10-20 16:30:42,070 [INFO] \U0001f7e2 Webhook server STARTED on port 5050
2025-10-20 16:30:42,353 [INFO] \U0001f4cb TabOrders initialized - All auto functions disabled
2025-10-20 16:31:32,733 [INFO] \U0001f534 Webhook server STOPPED
2025-10-20 16:31:32,734 [INFO] \U0001f534 Webhook server stopped
2025-10-20 16:31:32,734 [INFO] \U0001f534 Stopping background processes...
2025-10-20 16:31:34,739 [INFO] \U0001f534 MT5 connection closed
2025-10-20 16:31:37,535 [INFO] Serving on http://0.0.0.0:5050
2025-10-20 16:31:38,034 [INFO] \U0001f7e2 Webhook server STARTED on port 5050
2025-10-20 16:31:38,320 [INFO] \U0001f4cb TabOrders initialized - All auto functions disabled
2025-10-20 16:32:29,543 [INFO] Serving on http://0.0.0.0:5050
2025-10-20 16:32:30,050 [INFO] \U0001f7e2 Webhook server STARTED on port 5050
2025-10-20 16:32:30,313 [INFO] \U0001f4cb TabOrders initialized - All auto functions disabled
2025-10-20 16:32:44,508 [INFO] \U0001f504 Preparing order: Buy Now GBPUSD.s PY_VAR27 lots
2025-10-20 16:32:44,508 [INFO] \U0001f4ca Price info: bid=1.34361, ask=1.34374, using=1.34361
2025-10-20 16:32:44,531 [INFO] \u274c Order preparation failed: float() argument must be a string or a real number, not 'DoubleVar'
2025-10-20 16:32:53,297 [INFO] \U0001f534 Webhook server STOPPED
2025-10-20 16:32:53,297 [INFO] \U0001f534 Webhook server stopped
2025-10-20 16:32:53,299 [INFO] \U0001f534 Stopping background processes...
2025-10-20 16:32:55,308 [INFO] \U0001f534 MT5 connection closed
2025-10-20 16:33:22,326 [INFO] Serving on http://0.0.0.0:5050
2025-10-20 16:33:22,833 [INFO] \U0001f7e2 Webhook server STARTED on port 5050
2025-10-20 16:33:23,092 [INFO] \U0001f4cb TabOrders initialized - All auto functions disabled
2025-10-20 16:36:07,890 [INFO] \U0001f504 Preparing order: Buy Now GBPUSD.s PY_VAR27 lots
2025-10-20 16:36:07,894 [INFO] \U0001f4ca Price info: bid=1.34361, ask=1.34374, using=1.34361
2025-10-20 16:36:07,911 [INFO] \u274c Order preparation failed: float() argument must be a string or a real number, not 'DoubleVar'
2025-10-20 16:36:54,544 [INFO] \U0001f534 Webhook server STOPPED
2025-10-20 16:36:54,544 [INFO] \U0001f534 Webhook server stopped
2025-10-20 16:36:54,544 [INFO] \U0001f534 Stopping background processes...
2025-10-20 16:36:56,557 [INFO] \U0001f534 MT5 connection closed
2025-10-20 16:37:02,545 [INFO] Serving on http://0.0.0.0:5050
2025-10-20 16:37:03,050 [INFO] \U0001f7e2 Webhook server STARTED on port 5050
2025-10-20 16:37:03,312 [INFO] \U0001f4cb TabOrders initialized - All auto functions disabled
2025-10-20 16:37:10,460 [INFO] \U0001f504 Preparing order: Buy Now GBPUSD.s PY_VAR27 lots
2025-10-20 16:37:10,460 [INFO] \U0001f4ca Price info: bid=1.34361, ask=1.34374, using=1.34361
2025-10-20 16:37:10,478 [INFO] \u274c Order preparation failed: float() argument must be a string or a real number, not 'StringVar'
2025-10-20 16:39:14,479 [INFO] \U0001f534 Webhook server STOPPED
2025-10-20 16:39:14,479 [INFO] \U0001f534 Webhook server stopped
2025-10-20 16:39:14,479 [INFO] \U0001f534 Stopping background processes...
2025-10-20 16:39:16,492 [INFO] \U0001f534 MT5 connection closed
2025-10-20 16:39:18,989 [INFO] Serving on http://0.0.0.0:5050
2025-10-20 16:39:19,501 [INFO] \U0001f7e2 Webhook server STARTED on port 5050
2025-10-20 16:39:19,746 [INFO] \U0001f4cb TabOrders initialized - All auto functions disabled
2025-10-20 16:39:46,274 [INFO] \U0001f504 Preparing order: Buy Now GBPUSD.s 0.6 lots
2025-10-20 16:39:46,287 [INFO] \U0001f4ca Price info: bid=1.34354, ask=1.34367, using=1.34354
2025-10-20 16:39:46,300 [INFO] \U0001f4cb Order request prepared: {'action': 1, 'symbol': 'GBPUSD.s', 'volume': 0.6, 'type': 0, 'price': 1.34354, 'sl': 1.34328, 'tp': 1.3440599999999998, 'deviation': 10, 'magic': 38, 'comment': 'renew', 'type_filling': 0}
2025-10-20 16:39:46,300 [INFO] \U0001f4e4 Sending order request...
2025-10-20 16:39:46,531 [INFO] \U0001f4e4 Order send result: OrderSendResult(retcode=10009, deal=25474003, order=27798322, volume=0.6, price=1.34371, bid=0.0, ask=0.0, comment='Request executed', request_id=3296521957, retcode_external=0, request=TradeRequest(action=1, magic=38, order=0, symbol='GBPUSD.s', volume=0.6, price=1.34354, stoplimit=0.0, sl=1.34328, tp=1.3440599999999998, deviation=10, type=0, type_filling=0, type_time=0, expiration=0, comment='renew', position=0, position_by=0))
2025-10-20 16:39:46,542 [INFO] \U0001f7e2 Sending Buy Now: GBPUSD.s lot 0.6 @ 1.34367, SL 1.34328, TP 1.3440599999999998 - renew
2025-10-20 16:39:46,542 [INFO] \U0001f4ca Preparing Google Form submission for GBPUSD.s Buy Now
2025-10-20 16:39:47,385 [INFO] \U0001f4ca Order data submitted to Google Form: GBPUSD.s Buy Now
2025-10-20 16:39:47,385 [DEBUG] Google Form submitted successfully
2025-10-20 16:39:47,385 [INFO] \u2705 Google Form submission successful for GBPUSD.s
2025-10-20 16:39:47,643 [INFO] \u2705 Closed GBPUSD.s (BUY) | profit: -10.80
2025-10-20 16:45:38,994 [INFO] \U0001f534 Webhook server STOPPED
2025-10-20 16:45:38,994 [INFO] \U0001f534 Webhook server stopped
2025-10-20 16:45:38,994 [INFO] \U0001f534 Stopping background processes...
2025-10-20 16:45:40,999 [INFO] \U0001f534 MT5 connection closed
