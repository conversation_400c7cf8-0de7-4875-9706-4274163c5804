2025-08-31 14:14:08,944 [INFO] Serving on http://0.0.0.0:5050
2025-08-31 14:14:09,449 [INFO] \U0001f7e2 Webhook server STARTED on port 5050
2025-08-31 14:14:10,098 [INFO] \U0001f7e2 Orders processing loop started
2025-08-31 14:14:10,116 [INFO] \U0001f7e2 Orders processing loop started
2025-08-31 14:14:10,119 [INFO] \U0001f4cb TabOrders initialized - Auto BE enabled, auto refresh disabled
2025-08-31 14:14:11,331 [INFO] \u274c Refresh loop error (1/5): main thread is not in main loop
2025-08-31 14:19:07,253 [INFO] Serving on http://0.0.0.0:5050
2025-08-31 14:19:07,760 [INFO] \U0001f7e2 Webhook server STARTED on port 5050
2025-08-31 14:19:08,422 [INFO] \U0001f7e2 Orders processing loop started
2025-08-31 14:19:08,455 [INFO] \U0001f7e2 Orders processing loop started
2025-08-31 14:19:08,458 [INFO] \U0001f4cb TabOrders initialized - Auto BE enabled, auto refresh disabled
2025-08-31 14:19:09,973 [INFO] \u274c Refresh loop error (1/5): main thread is not in main loop
2025-08-31 14:19:19,013 [INFO] \U0001f4ca Preparing AI analysis for: My XAUUSD Bot (XAUUSD.iux ['H1'])
2025-08-31 14:19:49,089 [INFO] \U0001f4ca Preparing AI analysis for: My XAUUSD Bot (XAUUSD.iux ['H1'])
2025-08-31 14:20:02,303 [INFO] \U0001f534 Webhook server STOPPED
2025-08-31 14:20:02,304 [INFO] \U0001f534 Webhook server stopped
2025-08-31 14:20:02,304 [INFO] \U0001f534 Stopping background processes...
2025-08-31 14:20:04,306 [INFO] \U0001f534 MT5 connection closed
2025-08-31 14:21:36,508 [INFO] Serving on http://0.0.0.0:5050
2025-08-31 14:21:37,008 [INFO] \U0001f7e2 Webhook server STARTED on port 5050
2025-08-31 14:21:37,643 [INFO] \U0001f7e2 Orders processing loop started
2025-08-31 14:21:37,676 [INFO] \U0001f7e2 Orders processing loop started
2025-08-31 14:21:37,679 [INFO] \U0001f4cb TabOrders initialized - Auto BE enabled, auto refresh disabled
2025-08-31 14:21:52,247 [INFO] \U0001f4ca Preparing AI analysis for: My XAUUSD Bot (XAUUSD.iux ['H1'])
2025-08-31 14:22:06,788 [INFO] \U0001f916 Starting AI analysis: My XAUUSD Bot (XAUUSD.iux)
2025-08-31 14:22:27,611 [INFO] \u2705 AI analysis completed: My XAUUSD Bot
2025-08-31 14:27:07,327 [INFO] \u274c Order placement failed: 'Util' object has no attribute 'tabs'
2025-08-31 15:12:01,336 [ERROR] Exception on /webhook_ai_analysis [POST]
Traceback (most recent call last):
  File "c:\Users\<USER>\OneDrive\Documents\python\App\tab_webhook.py", line 639, in webhook_ai_analysis
    is_valid, error_message = self.verify_access_token()
AttributeError: 'TabWebhook' object has no attribute 'authenticate_request'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Documents\python\venv\lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\Users\<USER>\OneDrive\Documents\python\venv\lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\Users\<USER>\OneDrive\Documents\python\venv\lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\OneDrive\Documents\python\venv\lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
  File "c:\Users\<USER>\OneDrive\Documents\python\App\tab_webhook.py", line 710, in webhook_ai_analysis
    error_msg = f"AI Analysis webhook error: {str(e)}"
TypeError: TabWebhook.safe_ui_update() got an unexpected keyword argument 'level'
2025-08-31 15:15:28,024 [INFO] Serving on http://0.0.0.0:5050
2025-08-31 15:15:28,530 [INFO] \U0001f7e2 Webhook server STARTED on port 5050
2025-08-31 15:15:29,083 [INFO] \U0001f7e2 Orders processing loop started
2025-08-31 15:15:29,115 [INFO] \U0001f7e2 Orders processing loop started
2025-08-31 15:15:29,117 [INFO] \U0001f4cb TabOrders initialized - Auto BE enabled, auto refresh disabled
2025-08-31 15:36:15,247 [ERROR] Exception on /webhook_ai_analysis [POST]
Traceback (most recent call last):
  File "c:\Users\<USER>\OneDrive\Documents\python\App\tab_webhook.py", line 639, in webhook_ai_analysis
    is_valid, error_message = self.verify_access_token()
AttributeError: 'TabWebhook' object has no attribute 'authenticate_request'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Documents\python\venv\lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\Users\<USER>\OneDrive\Documents\python\venv\lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\Users\<USER>\OneDrive\Documents\python\venv\lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\OneDrive\Documents\python\venv\lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
  File "c:\Users\<USER>\OneDrive\Documents\python\App\tab_webhook.py", line 710, in webhook_ai_analysis
    error_msg = f"AI Analysis webhook error: {str(e)}"
TypeError: TabWebhook.safe_ui_update() got an unexpected keyword argument 'level'
2025-08-31 15:36:26,338 [ERROR] Exception on /webhook_ai_analysis [POST]
Traceback (most recent call last):
  File "c:\Users\<USER>\OneDrive\Documents\python\App\tab_webhook.py", line 639, in webhook_ai_analysis
    is_valid, error_message = self.verify_access_token()
AttributeError: 'TabWebhook' object has no attribute 'authenticate_request'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Documents\python\venv\lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\Users\<USER>\OneDrive\Documents\python\venv\lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\Users\<USER>\OneDrive\Documents\python\venv\lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\OneDrive\Documents\python\venv\lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
  File "c:\Users\<USER>\OneDrive\Documents\python\App\tab_webhook.py", line 710, in webhook_ai_analysis
    error_msg = f"AI Analysis webhook error: {str(e)}"
TypeError: TabWebhook.safe_ui_update() got an unexpected keyword argument 'level'
2025-08-31 16:36:33,746 [INFO] \U0001f534 Webhook server STOPPED
2025-08-31 16:36:33,748 [INFO] \U0001f534 Webhook server stopped
2025-08-31 16:36:33,748 [INFO] \U0001f534 Stopping background processes...
2025-08-31 16:36:35,761 [INFO] \U0001f534 MT5 connection closed
2025-08-31 16:57:31,139 [INFO] Serving on http://0.0.0.0:5050
2025-08-31 16:57:31,644 [INFO] \U0001f7e2 Webhook server STARTED on port 5050
2025-08-31 16:57:32,239 [INFO] \U0001f7e2 Orders processing loop started
2025-08-31 16:57:32,257 [INFO] \U0001f7e2 Orders processing loop started
2025-08-31 16:57:32,260 [INFO] \U0001f4cb TabOrders initialized - Auto BE enabled, auto refresh disabled
2025-08-31 16:57:46,011 [ERROR] Exception on /webhook_ai_analysis [POST]
Traceback (most recent call last):
  File "c:\Users\<USER>\OneDrive\Documents\python\App\tab_webhook.py", line 663, in webhook_ai_analysis
    symbol = self.util.get_symbol(symbol)
  File "c:\Users\<USER>\OneDrive\Documents\python\App\util.py", line 118, in get_symbol
    return self.config.symbols[symbol_var] + self.config.symbol_posfix.get()
KeyError: 'XAUUSD'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Documents\python\venv\lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\Users\<USER>\OneDrive\Documents\python\venv\lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\Users\<USER>\OneDrive\Documents\python\venv\lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\OneDrive\Documents\python\venv\lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
  File "c:\Users\<USER>\OneDrive\Documents\python\App\tab_webhook.py", line 711, in webhook_ai_analysis
    self.safe_ui_update(f"\u274c {error_msg}", "red", level=1)
TypeError: TabWebhook.safe_ui_update() got an unexpected keyword argument 'level'
2025-08-31 16:58:19,874 [ERROR] Exception on /webhook_ai_analysis [POST]
Traceback (most recent call last):
  File "c:\Users\<USER>\OneDrive\Documents\python\App\tab_webhook.py", line 663, in webhook_ai_analysis
    symbol = self.util.get_symbol(symbol)
  File "c:\Users\<USER>\OneDrive\Documents\python\App\util.py", line 118, in get_symbol
    return self.config.symbols[symbol_var] + self.config.symbol_posfix.get()
KeyError: 'XAUUSD'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Documents\python\venv\lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\Users\<USER>\OneDrive\Documents\python\venv\lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\Users\<USER>\OneDrive\Documents\python\venv\lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\OneDrive\Documents\python\venv\lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
  File "c:\Users\<USER>\OneDrive\Documents\python\App\tab_webhook.py", line 711, in webhook_ai_analysis
    self.safe_ui_update(f"\u274c {error_msg}", "red", level=1)
TypeError: TabWebhook.safe_ui_update() got an unexpected keyword argument 'level'
2025-08-31 17:01:52,068 [INFO] \U0001f534 Webhook server STOPPED
2025-08-31 17:01:52,069 [INFO] \U0001f534 Webhook server stopped
2025-08-31 17:01:52,070 [INFO] \U0001f534 Stopping background processes...
2025-08-31 17:01:54,079 [INFO] \U0001f534 MT5 connection closed
2025-08-31 17:04:02,451 [INFO] Serving on http://0.0.0.0:5050
2025-08-31 17:04:02,957 [INFO] \U0001f7e2 Webhook server STARTED on port 5050
2025-08-31 17:04:03,509 [INFO] \U0001f7e2 Orders processing loop started
2025-08-31 17:04:03,540 [INFO] \U0001f7e2 Orders processing loop started
2025-08-31 17:04:03,543 [INFO] \U0001f4cb TabOrders initialized - Auto BE enabled, auto refresh disabled
2025-08-31 17:04:26,358 [ERROR] Exception on /webhook_ai_analysis [POST]
Traceback (most recent call last):
  File "c:\Users\<USER>\OneDrive\Documents\python\App\tab_webhook.py", line 666, in webhook_ai_analysis
    self.safe_ui_update(f"\U0001f50d Debug: Test connection: OK {symbol}", "yellow", level=3)
TypeError: TabWebhook.safe_ui_update() got an unexpected keyword argument 'level'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Documents\python\venv\lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\Users\<USER>\OneDrive\Documents\python\venv\lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\Users\<USER>\OneDrive\Documents\python\venv\lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\OneDrive\Documents\python\venv\lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
  File "c:\Users\<USER>\OneDrive\Documents\python\App\tab_webhook.py", line 716, in webhook_ai_analysis
    self.safe_ui_update(f"\u274c {error_msg}", "red", level=1)
TypeError: TabWebhook.safe_ui_update() got an unexpected keyword argument 'level'
2025-08-31 17:06:35,762 [ERROR] Exception on /webhook_ai_analysis [POST]
Traceback (most recent call last):
  File "c:\Users\<USER>\OneDrive\Documents\python\App\tab_webhook.py", line 666, in webhook_ai_analysis
    self.safe_ui_update(f"\U0001f50d Debug: Test connection: OK {symbol}", "yellow", level=3)
TypeError: TabWebhook.safe_ui_update() got an unexpected keyword argument 'level'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Documents\python\venv\lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\Users\<USER>\OneDrive\Documents\python\venv\lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\Users\<USER>\OneDrive\Documents\python\venv\lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\OneDrive\Documents\python\venv\lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
  File "c:\Users\<USER>\OneDrive\Documents\python\App\tab_webhook.py", line 716, in webhook_ai_analysis
    self.safe_ui_update(f"\u274c {error_msg}", "red", level=1)
TypeError: TabWebhook.safe_ui_update() got an unexpected keyword argument 'level'
2025-08-31 17:16:42,078 [INFO] \U0001f534 Webhook server STOPPED
2025-08-31 17:16:42,080 [INFO] \U0001f534 Webhook server stopped
2025-08-31 17:16:42,080 [INFO] \U0001f534 Stopping background processes...
2025-08-31 17:16:44,090 [INFO] \U0001f534 MT5 connection closed
2025-08-31 17:25:24,216 [INFO] Serving on http://0.0.0.0:5050
2025-08-31 17:25:24,714 [INFO] \U0001f7e2 Webhook server STARTED on port 5050
2025-08-31 17:25:25,281 [INFO] \U0001f7e2 Orders processing loop started
2025-08-31 17:25:25,314 [INFO] \U0001f7e2 Orders processing loop started
2025-08-31 17:25:25,317 [INFO] \U0001f4cb TabOrders initialized - Auto BE enabled, auto refresh disabled
2025-08-31 17:51:44,572 [INFO] \U0001f534 Webhook server STOPPED
2025-08-31 17:51:44,574 [INFO] \U0001f534 Webhook server stopped
2025-08-31 17:51:44,574 [INFO] \U0001f534 Stopping background processes...
2025-08-31 17:51:46,587 [INFO] \U0001f534 MT5 connection closed
2025-08-31 17:54:50,277 [INFO] Serving on http://0.0.0.0:5050
2025-08-31 17:54:50,781 [INFO] \U0001f7e2 Webhook server STARTED on port 5050
2025-08-31 17:54:51,314 [INFO] \U0001f7e2 Orders processing loop started
2025-08-31 17:54:51,346 [INFO] \U0001f7e2 Orders processing loop started
2025-08-31 17:54:51,349 [INFO] \U0001f4cb TabOrders initialized - Auto BE enabled, auto refresh disabled
2025-08-31 17:54:59,069 [ERROR] Exception on /webhook_ai_analysis [POST]
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Documents\python\venv\lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\Users\<USER>\OneDrive\Documents\python\venv\lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\Users\<USER>\OneDrive\Documents\python\venv\lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\OneDrive\Documents\python\venv\lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
  File "c:\Users\<USER>\OneDrive\Documents\python\App\tab_webhook.py", line 633, in webhook_ai_analysis
    custom_prompt = data.get("prompt", "")
UnboundLocalError: local variable 'data' referenced before assignment
2025-08-31 17:59:05,144 [ERROR] Exception on /webhook_ai_analysis [POST]
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Documents\python\venv\lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\Users\<USER>\OneDrive\Documents\python\venv\lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\Users\<USER>\OneDrive\Documents\python\venv\lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\OneDrive\Documents\python\venv\lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
  File "c:\Users\<USER>\OneDrive\Documents\python\App\tab_webhook.py", line 633, in webhook_ai_analysis
    custom_prompt = data.get("prompt", "")
UnboundLocalError: local variable 'data' referenced before assignment
2025-08-31 18:03:21,634 [INFO] \U0001f534 Webhook server STOPPED
2025-08-31 18:03:21,636 [INFO] \U0001f534 Webhook server stopped
2025-08-31 18:03:21,636 [INFO] \U0001f534 Stopping background processes...
2025-08-31 18:03:23,641 [INFO] \U0001f534 MT5 connection closed
2025-08-31 18:03:34,982 [INFO] Serving on http://0.0.0.0:5050
2025-08-31 18:03:35,492 [INFO] \U0001f7e2 Webhook server STARTED on port 5050
2025-08-31 18:03:36,081 [INFO] \U0001f7e2 Orders processing loop started
2025-08-31 18:03:36,098 [INFO] \U0001f7e2 Orders processing loop started
2025-08-31 18:03:36,100 [INFO] \U0001f4cb TabOrders initialized - Auto BE enabled, auto refresh disabled
2025-08-31 18:09:07,215 [INFO] \U0001f534 Webhook server STOPPED
2025-08-31 18:09:07,215 [INFO] \U0001f534 Webhook server stopped
2025-08-31 18:09:07,217 [INFO] \U0001f534 Stopping background processes...
2025-08-31 18:09:09,222 [INFO] \U0001f534 MT5 connection closed
2025-08-31 18:09:35,616 [INFO] Serving on http://0.0.0.0:5050
2025-08-31 18:09:36,123 [INFO] \U0001f7e2 Webhook server STARTED on port 5050
2025-08-31 18:09:36,646 [INFO] \U0001f7e2 Orders processing loop started
2025-08-31 18:09:36,662 [INFO] \U0001f7e2 Orders processing loop started
2025-08-31 18:09:36,665 [INFO] \U0001f4cb TabOrders initialized - Auto BE enabled, auto refresh disabled
2025-08-31 18:10:05,409 [ERROR] Exception on /webhook_ai_analysis [POST]
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Documents\python\venv\lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\Users\<USER>\OneDrive\Documents\python\venv\lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\Users\<USER>\OneDrive\Documents\python\venv\lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\OneDrive\Documents\python\venv\lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
  File "c:\Users\<USER>\OneDrive\Documents\python\App\tab_webhook.py", line 635, in webhook_ai_analysis
    self.safe_ui_update(f"\U0001f50d Debug: Test connection: OK", "yellow", level=3)
TypeError: TabWebhook.safe_ui_update() got an unexpected keyword argument 'level'
2025-08-31 18:12:36,116 [INFO] \U0001f534 Webhook server STOPPED
2025-08-31 18:12:36,117 [INFO] \U0001f534 Webhook server stopped
2025-08-31 18:12:36,118 [INFO] \U0001f534 Stopping background processes...
2025-08-31 18:12:38,133 [INFO] \U0001f534 MT5 connection closed
2025-08-31 18:13:02,761 [INFO] Serving on http://0.0.0.0:5050
2025-08-31 18:13:03,263 [INFO] \U0001f7e2 Webhook server STARTED on port 5050
2025-08-31 18:13:03,825 [INFO] \U0001f7e2 Orders processing loop started
2025-08-31 18:13:03,858 [INFO] \U0001f7e2 Orders processing loop started
2025-08-31 18:13:03,861 [INFO] \U0001f4cb TabOrders initialized - Auto BE enabled, auto refresh disabled
2025-08-31 18:13:10,230 [ERROR] Exception on /webhook_ai_analysis [POST]
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Documents\python\venv\lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\Users\<USER>\OneDrive\Documents\python\venv\lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\Users\<USER>\OneDrive\Documents\python\venv\lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\OneDrive\Documents\python\venv\lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
  File "c:\Users\<USER>\OneDrive\Documents\python\App\tab_webhook.py", line 640, in webhook_ai_analysis
    self.safe_ui_update(f"\U0001f50d Debug: Test connection: OK", "yellow", level=3)
TypeError: TabWebhook.safe_ui_update() got an unexpected keyword argument 'level'
2025-08-31 18:13:31,152 [INFO] \U0001f534 Webhook server STOPPED
2025-08-31 18:13:31,153 [INFO] \U0001f534 Webhook server stopped
2025-08-31 18:13:31,153 [INFO] \U0001f534 Stopping background processes...
2025-08-31 18:13:33,160 [INFO] \U0001f534 MT5 connection closed
2025-08-31 18:15:49,512 [INFO] Serving on http://0.0.0.0:5050
2025-08-31 18:15:50,011 [INFO] \U0001f7e2 Webhook server STARTED on port 5050
2025-08-31 18:15:50,558 [INFO] \U0001f7e2 Orders processing loop started
2025-08-31 18:15:50,592 [INFO] \U0001f7e2 Orders processing loop started
2025-08-31 18:15:50,594 [INFO] \U0001f4cb TabOrders initialized - Auto BE enabled, auto refresh disabled
2025-08-31 18:16:57,588 [INFO] \U0001f534 Webhook server STOPPED
2025-08-31 18:16:57,588 [INFO] \U0001f534 Webhook server stopped
2025-08-31 18:16:57,589 [INFO] \U0001f534 Stopping background processes...
2025-08-31 18:16:59,591 [INFO] \U0001f534 MT5 connection closed
2025-08-31 18:17:13,455 [INFO] Serving on http://0.0.0.0:5050
2025-08-31 18:17:13,961 [INFO] \U0001f7e2 Webhook server STARTED on port 5050
2025-08-31 18:17:14,516 [INFO] \U0001f7e2 Orders processing loop started
2025-08-31 18:17:14,532 [INFO] \U0001f7e2 Orders processing loop started
2025-08-31 18:17:14,535 [INFO] \U0001f4cb TabOrders initialized - Auto BE enabled, auto refresh disabled
2025-08-31 18:17:27,554 [INFO] \U0001f534 Webhook server STOPPED
2025-08-31 18:17:27,554 [INFO] \U0001f534 Webhook server stopped
2025-08-31 18:17:27,555 [INFO] \U0001f534 Stopping background processes...
2025-08-31 18:17:29,559 [INFO] \U0001f534 MT5 connection closed
2025-08-31 18:18:01,973 [INFO] Serving on http://0.0.0.0:5050
2025-08-31 18:18:02,475 [INFO] \U0001f7e2 Webhook server STARTED on port 5050
2025-08-31 18:18:02,996 [INFO] \U0001f7e2 Orders processing loop started
2025-08-31 18:18:03,030 [INFO] \U0001f7e2 Orders processing loop started
2025-08-31 18:18:03,031 [INFO] \U0001f4cb TabOrders initialized - Auto BE enabled, auto refresh disabled
2025-08-31 18:18:07,042 [ERROR] Exception on /webhook_ai_analysis [POST]
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Documents\python\venv\lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\Users\<USER>\OneDrive\Documents\python\venv\lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\Users\<USER>\OneDrive\Documents\python\venv\lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\OneDrive\Documents\python\venv\lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
  File "c:\Users\<USER>\OneDrive\Documents\python\App\tab_webhook.py", line 640, in webhook_ai_analysis
    self.safe_ui_update(f"\U0001f50d Debug: Test connection: OK", "yellow", level=3)
TypeError: TabWebhook.safe_ui_update() got an unexpected keyword argument 'level'
2025-08-31 18:19:13,474 [INFO] \U0001f534 Webhook server STOPPED
2025-08-31 18:19:13,475 [INFO] \U0001f534 Webhook server stopped
2025-08-31 18:19:13,475 [INFO] \U0001f534 Stopping background processes...
2025-08-31 18:19:15,489 [INFO] \U0001f534 MT5 connection closed
2025-08-31 18:19:21,889 [INFO] Serving on http://0.0.0.0:5050
2025-08-31 18:19:22,399 [INFO] \U0001f7e2 Webhook server STARTED on port 5050
2025-08-31 18:19:22,940 [INFO] \U0001f7e2 Orders processing loop started
2025-08-31 18:19:22,956 [INFO] \U0001f7e2 Orders processing loop started
2025-08-31 18:19:22,958 [INFO] \U0001f4cb TabOrders initialized - Auto BE enabled, auto refresh disabled
2025-08-31 18:19:27,646 [ERROR] Exception on /webhook_ai_analysis [POST]
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Documents\python\venv\lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\Users\<USER>\OneDrive\Documents\python\venv\lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\Users\<USER>\OneDrive\Documents\python\venv\lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\OneDrive\Documents\python\venv\lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
  File "c:\Users\<USER>\OneDrive\Documents\python\App\tab_webhook.py", line 640, in webhook_ai_analysis
    self.safe_ui_update("\U0001f50d Debug: Test connection: OK", "yellow", level=3)
TypeError: TabWebhook.safe_ui_update() got an unexpected keyword argument 'level'
2025-08-31 18:22:11,976 [INFO] \U0001f534 Webhook server STOPPED
2025-08-31 18:22:11,977 [INFO] \U0001f534 Webhook server stopped
2025-08-31 18:22:11,978 [INFO] \U0001f534 Stopping background processes...
2025-08-31 18:22:13,982 [INFO] \U0001f534 MT5 connection closed
2025-08-31 18:22:19,379 [INFO] Serving on http://0.0.0.0:5050
2025-08-31 18:22:19,884 [INFO] \U0001f7e2 Webhook server STARTED on port 5050
2025-08-31 18:22:20,525 [INFO] \U0001f7e2 Orders processing loop started
2025-08-31 18:22:20,562 [INFO] \U0001f7e2 Orders processing loop started
2025-08-31 18:22:20,564 [INFO] \U0001f4cb TabOrders initialized - Auto BE enabled, auto refresh disabled
2025-08-31 18:22:28,130 [ERROR] Exception on /webhook_ai_analysis [POST]
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Documents\python\venv\lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\Users\<USER>\OneDrive\Documents\python\venv\lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\Users\<USER>\OneDrive\Documents\python\venv\lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\OneDrive\Documents\python\venv\lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
  File "c:\Users\<USER>\OneDrive\Documents\python\App\tab_webhook.py", line 640, in webhook_ai_analysis
    self.add_status_frame("\U0001f50d Debug: Test connection: OK", "yellow", level=3)
AttributeError: 'TabWebhook' object has no attribute 'add_status_frame'
2025-08-31 18:23:23,477 [INFO] \U0001f534 Webhook server STOPPED
2025-08-31 18:23:23,478 [INFO] \U0001f534 Webhook server stopped
2025-08-31 18:23:23,478 [INFO] \U0001f534 Stopping background processes...
2025-08-31 18:23:25,484 [INFO] \U0001f534 MT5 connection closed
2025-08-31 18:23:35,529 [INFO] Serving on http://0.0.0.0:5050
2025-08-31 18:23:36,027 [INFO] \U0001f7e2 Webhook server STARTED on port 5050
2025-08-31 18:23:36,612 [INFO] \U0001f7e2 Orders processing loop started
2025-08-31 18:23:36,645 [INFO] \U0001f7e2 Orders processing loop started
2025-08-31 18:23:36,648 [INFO] \U0001f4cb TabOrders initialized - Auto BE enabled, auto refresh disabled
2025-08-31 18:23:37,848 [INFO] \u274c Refresh loop error (1/5): main thread is not in main loop
2025-08-31 18:24:19,939 [INFO] \U0001f534 Webhook server STOPPED
2025-08-31 18:24:19,940 [INFO] \U0001f534 Webhook server stopped
2025-08-31 18:24:19,940 [INFO] \U0001f534 Stopping background processes...
2025-08-31 18:24:21,949 [INFO] \U0001f534 MT5 connection closed
2025-08-31 18:24:29,914 [INFO] Serving on http://0.0.0.0:5050
2025-08-31 18:24:30,418 [INFO] \U0001f7e2 Webhook server STARTED on port 5050
2025-08-31 18:24:31,043 [INFO] \U0001f7e2 Orders processing loop started
2025-08-31 18:24:31,062 [INFO] \U0001f7e2 Orders processing loop started
2025-08-31 18:24:31,064 [INFO] \U0001f4cb TabOrders initialized - Auto BE enabled, auto refresh disabled
2025-08-31 18:24:32,534 [INFO] \u274c Refresh loop error (1/5): main thread is not in main loop
2025-08-31 18:24:39,771 [INFO] \U0001f534 Webhook server STOPPED
2025-08-31 18:24:39,772 [INFO] \U0001f534 Webhook server stopped
2025-08-31 18:24:39,773 [INFO] \U0001f534 Stopping background processes...
2025-08-31 18:24:41,777 [INFO] \U0001f534 MT5 connection closed
2025-08-31 18:26:06,290 [INFO] Serving on http://0.0.0.0:5050
2025-08-31 18:26:06,802 [INFO] \U0001f7e2 Webhook server STARTED on port 5050
2025-08-31 18:26:07,345 [INFO] \U0001f7e2 Orders processing loop started
2025-08-31 18:26:07,377 [INFO] \U0001f7e2 Orders processing loop started
2025-08-31 18:26:07,380 [INFO] \U0001f4cb TabOrders initialized - Auto BE enabled, auto refresh disabled
2025-08-31 18:26:13,943 [INFO] \U0001f50d Debug: Test connection: OK
2025-08-31 18:27:21,665 [INFO] \U0001f534 Webhook server STOPPED
2025-08-31 18:27:21,667 [INFO] \U0001f534 Webhook server stopped
2025-08-31 18:27:21,668 [INFO] \U0001f534 Stopping background processes...
2025-08-31 18:27:23,670 [INFO] \U0001f534 MT5 connection closed
2025-08-31 18:27:42,861 [INFO] Serving on http://0.0.0.0:5050
2025-08-31 18:27:43,363 [INFO] \U0001f7e2 Webhook server STARTED on port 5050
2025-08-31 18:27:43,880 [INFO] \U0001f7e2 Orders processing loop started
2025-08-31 18:27:43,911 [INFO] \U0001f7e2 Orders processing loop started
2025-08-31 18:27:43,913 [INFO] \U0001f4cb TabOrders initialized - Auto BE enabled, auto refresh disabled
2025-08-31 18:27:48,344 [INFO] \U0001f50d Debug: Test connection: OK
2025-08-31 18:27:52,934 [INFO] \U0001f916 AI Analysis started: XAUUSD.iux ['H1'] via GPT
2025-08-31 18:28:20,754 [INFO] \u2705 AI Analysis completed: XAUUSD.iux via GPT
