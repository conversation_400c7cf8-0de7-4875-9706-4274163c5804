2025-09-09 12:35:55,083 [INFO] Serving on http://0.0.0.0:5050
2025-09-09 12:35:55,595 [INFO] \U0001f7e2 Webhook server STARTED on port 5050
2025-09-09 12:35:56,074 [INFO] \U0001f7e2 Orders processing loop started
2025-09-09 12:35:56,086 [DEBUG] Processing auto BE/TP for 1 symbols: {'XAUUSD.iux'}
2025-09-09 12:35:56,086 [INFO] \U0001f7e2 Orders processing loop started
2025-09-09 12:35:56,086 [DEBUG] Processing 1 positions for XAUUSD.iux
2025-09-09 12:35:56,087 [DEBUG] Processing 1 positions for  (SL: True, TP: True)
2025-09-09 12:35:56,116 [DEBUG] DEBUG: Magic number usage - Ticket: 348075168, Magic: 2155, SL Points: 2155, Distance: 21.55000, Symbol: XAUUSD.iux
2025-09-09 12:35:56,117 [INFO] \U0001f4cb TabOrders initialized - Auto BE enabled, auto refresh disabled
2025-09-09 13:06:14,876 [INFO] Serving on http://0.0.0.0:5050
2025-09-09 13:06:15,380 [INFO] \U0001f7e2 Webhook server STARTED on port 5050
2025-09-09 13:06:15,901 [INFO] \U0001f7e2 Orders processing loop started
2025-09-09 13:06:15,929 [INFO] \U0001f7e2 Orders processing loop started
2025-09-09 13:06:15,929 [DEBUG] Processing auto BE/TP for 1 symbols: {'XAUUSD.iux'}
2025-09-09 13:06:15,929 [DEBUG] Processing 1 positions for XAUUSD.iux
2025-09-09 13:06:15,930 [DEBUG] Processing 1 positions for  (SL: True, TP: True)
2025-09-09 13:06:15,957 [DEBUG] DEBUG: Magic number usage - Ticket: 348075168, Magic: 2155, SL Points: 2155, Distance: 21.55000, Symbol: XAUUSD.iux
2025-09-09 13:06:15,959 [INFO] \U0001f4cb TabOrders initialized - Auto BE enabled, auto refresh disabled
2025-09-09 13:06:17,178 [INFO] \U0001f522 #348075168: Magic=2155, SL Points=2155
2025-09-09 13:06:17,179 [DEBUG] #348075168: Entry=3593.70000, Current=3644.86000, SL=3593.80000, TP=3680.40000
2025-09-09 13:06:17,179 [DEBUG] #348075168: point_sl=21.55000, point_be=-3599.99600, is_moving_tp=True
2025-09-09 13:06:17,179 [DEBUG] #348075168: BUY Moving TP - Current: 3644.86000, Trigger: -6.29600
2025-09-09 13:06:17,181 [DEBUG] #348075168: BUY Moving TP factor -167.05317865429234 - New SL: -49.29600, New TP: 37.30400
2025-09-09 13:06:17,181 [DEBUG] #348075168: Modifying order - SL: 3593.80000 -> -49.29600, TP: 3680.40000 -> 37.30400
2025-09-09 13:06:17,196 [ERROR] #348075168 - ERROR in order modification: Order send failed: AutoTrading disabled by client
NoneType: None
2025-09-09 13:06:17,198 [INFO] \u274c #348075168 - ERROR in order modification: Order send failed: AutoTrading disabled by client
2025-09-09 13:06:17,199 [INFO] DEBUG: Order processing - Symbol: XAUUSD.iux, Group: , Total: 1, Processed: 1, Action: SL+TP
2025-09-09 13:06:17,199 [INFO] \U0001f4e6 SL+TP Processing: XAUUSD.iux (All) - 1/1 orders
2025-09-09 13:06:17,200 [DEBUG] Processed 1  positions for SL+TP
2025-09-09 13:06:25,163 [INFO] \U0001f534 Webhook server STOPPED
2025-09-09 13:06:25,163 [INFO] \U0001f534 Webhook server stopped
2025-09-09 13:06:25,164 [INFO] \U0001f534 Stopping background processes...
2025-09-09 13:06:27,171 [INFO] \U0001f534 Stopping AI bot scheduler...
2025-09-09 13:06:29,184 [INFO] \U0001f534 MT5 connection closed
