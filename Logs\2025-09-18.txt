2025-09-18 15:33:09,645 [INFO] Serving on http://0.0.0.0:5050
2025-09-18 15:33:10,155 [INFO] \U0001f7e2 Webhook server STARTED on port 5050
2025-09-18 15:33:11,076 [INFO] \U0001f7e2 Orders processing loop started
2025-09-18 15:33:11,130 [INFO] \U0001f7e2 Orders processing loop started
2025-09-18 15:33:11,130 [DEBUG] No positions found for auto BE/TP processing
2025-09-18 15:33:11,132 [INFO] \U0001f4cb TabOrders initialized - Auto BE enabled, auto refresh disabled
2025-09-18 15:33:13,025 [INFO] \u274c Refresh loop error (1/5): main thread is not in main loop
2025-09-18 15:33:43,295 [DEBUG] No positions found for auto BE/TP processing
2025-09-18 15:34:13,366 [DEBUG] No positions found for auto BE/TP processing
2025-09-18 15:34:43,444 [DEBUG] No positions found for auto BE/TP processing
2025-09-18 15:35:13,531 [DEBUG] No positions found for auto BE/TP processing
2025-09-18 15:37:41,422 [INFO] Serving on http://0.0.0.0:5050
2025-09-18 15:37:41,927 [INFO] \U0001f7e2 Webhook server STARTED on port 5050
2025-09-18 15:37:42,738 [INFO] \U0001f7e2 Orders processing loop started
2025-09-18 15:37:42,792 [INFO] \U0001f7e2 Orders processing loop started
2025-09-18 15:37:42,792 [DEBUG] No positions found for auto BE/TP processing
2025-09-18 15:37:42,794 [INFO] \U0001f4cb TabOrders initialized - Auto BE enabled, auto refresh disabled
2025-09-18 15:37:44,539 [INFO] \u274c Refresh loop error (1/5): main thread is not in main loop
2025-09-18 15:38:14,611 [DEBUG] No positions found for auto BE/TP processing
2025-09-18 15:38:44,684 [DEBUG] No positions found for auto BE/TP processing
2025-09-18 15:39:14,746 [DEBUG] No positions found for auto BE/TP processing
2025-09-18 15:39:18,719 [INFO] \U0001f504 Preparing order: Buy Now GBPUSD.iux 0.5 lots
2025-09-18 15:39:18,738 [INFO] \U0001f4ca Price info: bid=1.36371, ask=1.3638, using=1.36371
2025-09-18 15:39:18,756 [INFO] \U0001f4cb Order request prepared: {'action': 1, 'symbol': 'GBPUSD.iux', 'volume': 0.5, 'type': 0, 'price': 1.36371, 'sl': 1.36362, 'tp': 1.3640699999999997, 'deviation': 10, 'magic': 17, 'comment': 'renew', 'type_filling': 0}
2025-09-18 15:39:18,757 [INFO] \U0001f4e4 Sending order request...
2025-09-18 15:39:18,775 [INFO] \U0001f4e4 Order send result: OrderSendResult(retcode=10027, deal=0, order=0, volume=0.0, price=0.0, bid=0.0, ask=0.0, comment='AutoTrading disabled by client', request_id=0, retcode_external=0, request=TradeRequest(action=1, magic=17, order=0, symbol='GBPUSD.iux', volume=0.5, price=1.36371, stoplimit=0.0, sl=1.36362, tp=1.3640699999999997, deviation=10, type=0, type_filling=0, type_time=0, expiration=0, comment='renew', position=0, position_by=0))
2025-09-18 15:39:18,776 [INFO] \u274c Execute failed: AutoTrading disabled by client
2025-09-18 15:39:18,776 [INFO] \U0001f504 Preparing order: Buy Now GBPUSD.iux 0.5 lots
2025-09-18 15:39:18,797 [INFO] \U0001f4ca Price info: bid=1.36371, ask=1.3638, using=1.36371
2025-09-18 15:39:18,818 [INFO] \U0001f4cb Order request prepared: {'action': 1, 'symbol': 'GBPUSD.iux', 'volume': 0.5, 'type': 0, 'price': 1.36371, 'sl': 1.36362, 'tp': 1.3640699999999997, 'deviation': 10, 'magic': 17, 'comment': 'renew', 'type_filling': 0}
2025-09-18 15:39:18,819 [INFO] \U0001f4e4 Sending order request...
2025-09-18 15:39:18,837 [INFO] \U0001f4e4 Order send result: OrderSendResult(retcode=10027, deal=0, order=0, volume=0.0, price=0.0, bid=0.0, ask=0.0, comment='AutoTrading disabled by client', request_id=0, retcode_external=0, request=TradeRequest(action=1, magic=17, order=0, symbol='GBPUSD.iux', volume=0.5, price=1.36371, stoplimit=0.0, sl=1.36362, tp=1.3640699999999997, deviation=10, type=0, type_filling=0, type_time=0, expiration=0, comment='renew', position=0, position_by=0))
2025-09-18 15:39:18,837 [INFO] \u274c Execute failed: AutoTrading disabled by client
2025-09-18 15:39:35,808 [INFO] \U0001f504 Preparing order: Buy Now GBPUSD.iux 0.5 lots
2025-09-18 15:39:35,828 [INFO] \U0001f4ca Price info: bid=1.36374, ask=1.36383, using=1.36374
2025-09-18 15:39:35,848 [INFO] \U0001f4cb Order request prepared: {'action': 1, 'symbol': 'GBPUSD.iux', 'volume': 0.5, 'type': 0, 'price': 1.36374, 'sl': 1.3636499999999998, 'tp': 1.3641000000000005, 'deviation': 10, 'magic': 18, 'comment': 'renew', 'type_filling': 0}
2025-09-18 15:39:35,849 [INFO] \U0001f4e4 Sending order request...
2025-09-18 15:39:35,907 [INFO] \U0001f4e4 Order send result: OrderSendResult(retcode=10009, deal=150076474, order=356039416, volume=0.5, price=1.36383, bid=1.36374, ask=1.36383, comment='Request executed', request_id=1760797206, retcode_external=0, request=TradeRequest(action=1, magic=18, order=0, symbol='GBPUSD.iux', volume=0.5, price=1.36374, stoplimit=0.0, sl=1.3636499999999998, tp=1.3641000000000005, deviation=10, type=0, type_filling=0, type_time=0, expiration=0, comment='renew', position=0, position_by=0))
2025-09-18 15:39:35,908 [INFO] \U0001f7e2 Sending Buy Now: GBPUSD.iux lot 0.5 @ 1.36383, SL 1.3636499999999998, TP 1.3641000000000005 - renew
2025-09-18 15:39:35,908 [INFO] \U0001f504 Preparing order: Buy Now GBPUSD.iux 0.5 lots
2025-09-18 15:39:35,933 [INFO] \U0001f4ca Price info: bid=1.36374, ask=1.36383, using=1.36374
2025-09-18 15:39:35,956 [INFO] \U0001f4cb Order request prepared: {'action': 1, 'symbol': 'GBPUSD.iux', 'volume': 0.5, 'type': 0, 'price': 1.36374, 'sl': 1.3636499999999998, 'tp': 1.3641000000000005, 'deviation': 10, 'magic': 18, 'comment': 'renew', 'type_filling': 0}
2025-09-18 15:39:35,956 [INFO] \U0001f4e4 Sending order request...
2025-09-18 15:39:36,013 [INFO] \U0001f4e4 Order send result: OrderSendResult(retcode=10009, deal=150076476, order=356039418, volume=0.5, price=1.36383, bid=1.36374, ask=1.36383, comment='Request executed', request_id=1760797207, retcode_external=0, request=TradeRequest(action=1, magic=18, order=0, symbol='GBPUSD.iux', volume=0.5, price=1.36374, stoplimit=0.0, sl=1.3636499999999998, tp=1.3641000000000005, deviation=10, type=0, type_filling=0, type_time=0, expiration=0, comment='renew', position=0, position_by=0))
2025-09-18 15:39:36,014 [INFO] \U0001f7e2 Sending Buy Now: GBPUSD.iux lot 0.5 @ 1.36383, SL 1.3636499999999998, TP 1.3641000000000005 - renew
2025-09-18 15:39:44,825 [DEBUG] Processing auto BE/TP for 1 symbols: {'GBPUSD.iux'}
2025-09-18 15:39:44,825 [DEBUG] Processing 2 positions for GBPUSD.iux
2025-09-18 15:39:44,827 [DEBUG] Processing 2 positions for  (SL: True, TP: True)
2025-09-18 15:39:44,865 [DEBUG] DEBUG: Magic number usage - Ticket: 356039416, Magic: 18, SL Points: 18, Distance: 0.00018, Symbol: GBPUSD.iux
2025-09-18 15:39:44,867 [INFO] \U0001f522 #356039416: Magic=18, SL Points=18
2025-09-18 15:39:44,868 [DEBUG] #356039416: Entry=1.36383, Current=1.36370, SL=1.36365, TP=1.36410
2025-09-18 15:39:44,869 [DEBUG] #356039416: point_sl=0.00018, point_be=-1.36400, is_moving_tp=True
2025-09-18 15:39:44,869 [DEBUG] #356039416: BUY Moving TP - Current: 1.36370, Trigger: -0.00017
2025-09-18 15:39:44,869 [DEBUG] #356039416: BUY Moving TP factor -7577.757549999999 - New SL: -0.00043, New TP: 0.00069
2025-09-18 15:39:44,870 [DEBUG] #356039416: Modifying order - SL: 1.36365 -> -0.00043, TP: 1.36410 -> 0.00069
2025-09-18 15:39:44,889 [ERROR] #356039416 - ERROR in order modification: Order send failed: Invalid stops
NoneType: None
2025-09-18 15:39:44,892 [INFO] \u274c #356039416 - ERROR in order modification: Order send failed: Invalid stops
2025-09-18 15:39:44,912 [DEBUG] DEBUG: Magic number usage - Ticket: 356039418, Magic: 18, SL Points: 18, Distance: 0.00018, Symbol: GBPUSD.iux
2025-09-18 15:39:44,915 [INFO] \U0001f522 #356039418: Magic=18, SL Points=18
2025-09-18 15:39:44,915 [DEBUG] #356039418: Entry=1.36383, Current=1.36370, SL=1.36365, TP=1.36410
2025-09-18 15:39:44,915 [DEBUG] #356039418: point_sl=0.00018, point_be=-1.36400, is_moving_tp=True
2025-09-18 15:39:44,916 [DEBUG] #356039418: BUY Moving TP - Current: 1.36370, Trigger: -0.00017
2025-09-18 15:39:44,916 [DEBUG] #356039418: BUY Moving TP factor -7577.757549999999 - New SL: -0.00043, New TP: 0.00069
2025-09-18 15:39:44,916 [DEBUG] #356039418: Modifying order - SL: 1.36365 -> -0.00043, TP: 1.36410 -> 0.00069
2025-09-18 15:39:44,938 [ERROR] #356039418 - ERROR in order modification: Order send failed: Invalid stops
NoneType: None
2025-09-18 15:39:45,047 [INFO] \u274c #356039418 - ERROR in order modification: Order send failed: Invalid stops
2025-09-18 15:39:45,048 [INFO] DEBUG: Order processing - Symbol: GBPUSD.iux, Group: , Total: 2, Processed: 2, Action: SL+TP
2025-09-18 15:39:45,049 [INFO] \U0001f4e6 SL+TP Processing: GBPUSD.iux (All) - 2/2 orders
2025-09-18 15:39:45,049 [DEBUG] Processed 2  positions for SL+TP
2025-09-18 15:40:15,112 [DEBUG] Processing auto BE/TP for 1 symbols: {'GBPUSD.iux'}
2025-09-18 15:40:15,112 [DEBUG] Processing 2 positions for GBPUSD.iux
2025-09-18 15:40:15,113 [DEBUG] Processing 2 positions for  (SL: True, TP: True)
2025-09-18 15:40:15,151 [DEBUG] DEBUG: Magic number usage - Ticket: 356039416, Magic: 18, SL Points: 18, Distance: 0.00018, Symbol: GBPUSD.iux
2025-09-18 15:40:15,151 [INFO] \U0001f522 #356039416: Magic=18, SL Points=18
2025-09-18 15:40:15,152 [DEBUG] #356039416: Entry=1.36383, Current=1.36370, SL=1.36365, TP=1.36410
2025-09-18 15:40:15,152 [DEBUG] #356039416: point_sl=0.00018, point_be=-1.36400, is_moving_tp=True
2025-09-18 15:40:15,153 [DEBUG] #356039416: BUY Moving TP - Current: 1.36370, Trigger: -0.00017
2025-09-18 15:40:15,153 [DEBUG] #356039416: BUY Moving TP factor -7577.757549999999 - New SL: -0.00043, New TP: 0.00069
2025-09-18 15:40:15,154 [DEBUG] #356039416: Modifying order - SL: 1.36365 -> -0.00043, TP: 1.36410 -> 0.00069
2025-09-18 15:40:15,177 [ERROR] #356039416 - ERROR in order modification: Order send failed: Invalid stops
NoneType: None
2025-09-18 15:40:15,182 [INFO] \u274c #356039416 - ERROR in order modification: Order send failed: Invalid stops
2025-09-18 15:40:15,203 [DEBUG] DEBUG: Magic number usage - Ticket: 356039418, Magic: 18, SL Points: 18, Distance: 0.00018, Symbol: GBPUSD.iux
2025-09-18 15:40:15,204 [INFO] \U0001f522 #356039418: Magic=18, SL Points=18
2025-09-18 15:40:15,204 [DEBUG] #356039418: Entry=1.36383, Current=1.36370, SL=1.36365, TP=1.36410
2025-09-18 15:40:15,205 [DEBUG] #356039418: point_sl=0.00018, point_be=-1.36400, is_moving_tp=True
2025-09-18 15:40:15,205 [DEBUG] #356039418: BUY Moving TP - Current: 1.36370, Trigger: -0.00017
2025-09-18 15:40:15,206 [DEBUG] #356039418: BUY Moving TP factor -7577.757549999999 - New SL: -0.00043, New TP: 0.00069
2025-09-18 15:40:15,206 [DEBUG] #356039418: Modifying order - SL: 1.36365 -> -0.00043, TP: 1.36410 -> 0.00069
2025-09-18 15:40:15,226 [ERROR] #356039418 - ERROR in order modification: Order send failed: Invalid stops
NoneType: None
2025-09-18 15:40:15,336 [INFO] \u274c #356039418 - ERROR in order modification: Order send failed: Invalid stops
2025-09-18 15:40:15,336 [INFO] DEBUG: Order processing - Symbol: GBPUSD.iux, Group: , Total: 2, Processed: 2, Action: SL+TP
2025-09-18 15:40:15,337 [INFO] \U0001f4e6 SL+TP Processing: GBPUSD.iux (All) - 2/2 orders
2025-09-18 15:40:15,338 [DEBUG] Processed 2  positions for SL+TP
2025-09-18 15:40:45,418 [DEBUG] No positions found for auto BE/TP processing
2025-09-18 15:41:15,502 [DEBUG] No positions found for auto BE/TP processing
2025-09-18 15:41:40,347 [INFO] \U0001f504 Preparing order: Buy Now GBPUSD.s 0.5 lots
2025-09-18 15:41:40,370 [INFO] \U0001f4ca Price info: bid=1.36365, ask=1.36377, using=1.36365
2025-09-18 15:41:40,390 [INFO] \U0001f4cb Order request prepared: {'action': 1, 'symbol': 'GBPUSD.s', 'volume': 0.5, 'type': 0, 'price': 1.36365, 'sl': 1.3635300000000001, 'tp': 1.3641299999999996, 'deviation': 10, 'magic': 23, 'comment': 'renew', 'type_filling': 0}
2025-09-18 15:41:40,391 [INFO] \U0001f4e4 Sending order request...
2025-09-18 15:41:40,409 [INFO] \U0001f4e4 Order send result: OrderSendResult(retcode=10027, deal=0, order=0, volume=0.0, price=0.0, bid=0.0, ask=0.0, comment='AutoTrading disabled by client', request_id=0, retcode_external=0, request=TradeRequest(action=1, magic=23, order=0, symbol='GBPUSD.s', volume=0.5, price=1.36365, stoplimit=0.0, sl=1.3635300000000001, tp=1.3641299999999996, deviation=10, type=0, type_filling=0, type_time=0, expiration=0, comment='renew', position=0, position_by=0))
2025-09-18 15:41:40,409 [INFO] \u274c Execute failed: AutoTrading disabled by client
2025-09-18 15:41:40,410 [INFO] \U0001f504 Preparing order: Buy Now GBPUSD.s 0.5 lots
2025-09-18 15:41:40,430 [INFO] \U0001f4ca Price info: bid=1.36365, ask=1.36377, using=1.36365
2025-09-18 15:41:40,450 [INFO] \U0001f4cb Order request prepared: {'action': 1, 'symbol': 'GBPUSD.s', 'volume': 0.5, 'type': 0, 'price': 1.36365, 'sl': 1.3635300000000001, 'tp': 1.3641299999999996, 'deviation': 10, 'magic': 23, 'comment': 'renew', 'type_filling': 0}
2025-09-18 15:41:40,450 [INFO] \U0001f4e4 Sending order request...
2025-09-18 15:41:40,470 [INFO] \U0001f4e4 Order send result: OrderSendResult(retcode=10027, deal=0, order=0, volume=0.0, price=0.0, bid=0.0, ask=0.0, comment='AutoTrading disabled by client', request_id=0, retcode_external=0, request=TradeRequest(action=1, magic=23, order=0, symbol='GBPUSD.s', volume=0.5, price=1.36365, stoplimit=0.0, sl=1.3635300000000001, tp=1.3641299999999996, deviation=10, type=0, type_filling=0, type_time=0, expiration=0, comment='renew', position=0, position_by=0))
2025-09-18 15:41:40,471 [INFO] \u274c Execute failed: AutoTrading disabled by client
2025-09-18 15:41:45,566 [DEBUG] No positions found for auto BE/TP processing
2025-09-18 15:42:15,633 [DEBUG] No positions found for auto BE/TP processing
2025-09-18 15:42:33,097 [INFO] \U0001f534 Webhook server STOPPED
2025-09-18 15:42:33,099 [INFO] \U0001f534 Webhook server stopped
2025-09-18 15:42:33,101 [INFO] \U0001f534 Stopping background processes...
2025-09-18 15:42:35,116 [INFO] \U0001f534 Stopping AI bot scheduler...
2025-09-18 15:42:37,127 [INFO] \U0001f534 MT5 connection closed
2025-09-18 15:44:26,645 [INFO] Serving on http://0.0.0.0:5050
2025-09-18 15:44:27,155 [INFO] \U0001f7e2 Webhook server STARTED on port 5050
2025-09-18 15:44:27,886 [INFO] \U0001f7e2 Orders processing loop started
2025-09-18 15:44:27,932 [INFO] \U0001f7e2 Orders processing loop started
2025-09-18 15:44:27,932 [DEBUG] No positions found for auto BE/TP processing
2025-09-18 15:44:27,934 [INFO] \U0001f4cb TabOrders initialized - Auto BE enabled, auto refresh disabled
2025-09-18 15:44:29,351 [INFO] \u274c Refresh loop error (1/5): main thread is not in main loop
2025-09-18 15:44:59,419 [DEBUG] No positions found for auto BE/TP processing
2025-09-18 15:45:29,500 [DEBUG] No positions found for auto BE/TP processing
2025-09-18 15:45:54,267 [INFO] \U0001f504 Preparing order: Buy Now GBPUSD.iux 0.5 lots
2025-09-18 15:45:54,287 [INFO] \U0001f4ca Price info: bid=1.36399, ask=1.36408, using=1.36399
2025-09-18 15:45:54,308 [INFO] \U0001f4cb Order request prepared: {'action': 1, 'symbol': 'GBPUSD.iux', 'volume': 0.5, 'type': 0, 'price': 1.36399, 'sl': 1.3638100000000002, 'tp': 1.3643499999999997, 'deviation': 10, 'magic': 26, 'comment': 'renew', 'type_filling': 0}
2025-09-18 15:45:54,309 [INFO] \U0001f4e4 Sending order request...
2025-09-18 15:45:54,329 [INFO] \U0001f4e4 Order send result: OrderSendResult(retcode=10027, deal=0, order=0, volume=0.0, price=0.0, bid=0.0, ask=0.0, comment='AutoTrading disabled by client', request_id=0, retcode_external=0, request=TradeRequest(action=1, magic=26, order=0, symbol='GBPUSD.iux', volume=0.5, price=1.36399, stoplimit=0.0, sl=1.3638100000000002, tp=1.3643499999999997, deviation=10, type=0, type_filling=0, type_time=0, expiration=0, comment='renew', position=0, position_by=0))
2025-09-18 15:45:54,330 [INFO] \u274c Execute failed: AutoTrading disabled by client
2025-09-18 15:45:54,331 [INFO] \U0001f504 Preparing order: Buy Now GBPUSD.iux 0.5 lots
2025-09-18 15:45:54,352 [INFO] \U0001f4ca Price info: bid=1.36399, ask=1.36408, using=1.36399
2025-09-18 15:45:54,373 [INFO] \U0001f4cb Order request prepared: {'action': 1, 'symbol': 'GBPUSD.iux', 'volume': 0.5, 'type': 0, 'price': 1.36399, 'sl': 1.3638100000000002, 'tp': 1.3643499999999997, 'deviation': 10, 'magic': 26, 'comment': 'renew', 'type_filling': 0}
2025-09-18 15:45:54,373 [INFO] \U0001f4e4 Sending order request...
2025-09-18 15:45:54,395 [INFO] \U0001f4e4 Order send result: OrderSendResult(retcode=10027, deal=0, order=0, volume=0.0, price=0.0, bid=0.0, ask=0.0, comment='AutoTrading disabled by client', request_id=0, retcode_external=0, request=TradeRequest(action=1, magic=26, order=0, symbol='GBPUSD.iux', volume=0.5, price=1.36399, stoplimit=0.0, sl=1.3638100000000002, tp=1.3643499999999997, deviation=10, type=0, type_filling=0, type_time=0, expiration=0, comment='renew', position=0, position_by=0))
2025-09-18 15:45:54,396 [INFO] \u274c Execute failed: AutoTrading disabled by client
2025-09-18 15:45:54,417 [INFO] \u274c Try to close filter: No open positions.
2025-09-18 15:45:59,569 [DEBUG] No positions found for auto BE/TP processing
2025-09-18 15:46:28,806 [INFO] \U0001f504 Preparing order: Buy Now GBPUSD.s 0.5 lots
2025-09-18 15:46:28,833 [INFO] \U0001f4ca Price info: bid=1.36387, ask=1.3639999999999999, using=1.36387
2025-09-18 15:46:28,854 [INFO] \U0001f4cb Order request prepared: {'action': 1, 'symbol': 'GBPUSD.s', 'volume': 0.5, 'type': 0, 'price': 1.36387, 'sl': 1.36361, 'tp': 1.3643899999999998, 'deviation': 10, 'magic': 38, 'comment': 'renew', 'type_filling': 0}
2025-09-18 15:46:28,855 [INFO] \U0001f4e4 Sending order request...
2025-09-18 15:46:29,067 [INFO] \U0001f4e4 Order send result: OrderSendResult(retcode=10009, deal=22124846, order=24142737, volume=0.5, price=1.364, bid=0.0, ask=0.0, comment='Request executed', request_id=2649149482, retcode_external=0, request=TradeRequest(action=1, magic=38, order=0, symbol='GBPUSD.s', volume=0.5, price=1.36387, stoplimit=0.0, sl=1.36361, tp=1.3643899999999998, deviation=10, type=0, type_filling=0, type_time=0, expiration=0, comment='renew', position=0, position_by=0))
2025-09-18 15:46:29,068 [INFO] \U0001f7e2 Sending Buy Now: GBPUSD.s lot 0.5 @ 1.3639999999999999, SL 1.36361, TP 1.3643899999999998 - renew
2025-09-18 15:46:29,068 [INFO] \U0001f504 Preparing order: Buy Now GBPUSD.s 0.5 lots
2025-09-18 15:46:29,088 [INFO] \U0001f4ca Price info: bid=1.36387, ask=1.3639999999999999, using=1.36387
2025-09-18 15:46:29,108 [INFO] \U0001f4cb Order request prepared: {'action': 1, 'symbol': 'GBPUSD.s', 'volume': 0.5, 'type': 0, 'price': 1.36387, 'sl': 1.36361, 'tp': 1.3643899999999998, 'deviation': 10, 'magic': 38, 'comment': 'renew', 'type_filling': 0}
2025-09-18 15:46:29,110 [INFO] \U0001f4e4 Sending order request...
2025-09-18 15:46:29,318 [INFO] \U0001f4e4 Order send result: OrderSendResult(retcode=10009, deal=22124847, order=24142738, volume=0.5, price=1.364, bid=0.0, ask=0.0, comment='Request executed', request_id=2649149483, retcode_external=0, request=TradeRequest(action=1, magic=38, order=0, symbol='GBPUSD.s', volume=0.5, price=1.36387, stoplimit=0.0, sl=1.36361, tp=1.3643899999999998, deviation=10, type=0, type_filling=0, type_time=0, expiration=0, comment='renew', position=0, position_by=0))
2025-09-18 15:46:29,319 [INFO] \U0001f7e2 Sending Buy Now: GBPUSD.s lot 0.5 @ 1.3639999999999999, SL 1.36361, TP 1.3643899999999998 - renew
2025-09-18 15:46:29,533 [INFO] \u2705 Closed GBPUSD.s (BUY) | profit: -6.50
2025-09-18 15:46:29,783 [INFO] \u2705 Closed GBPUSD.s (BUY) | profit: -6.50
2025-09-18 15:46:29,783 [DEBUG] No positions found for auto BE/TP processing
2025-09-18 15:46:59,857 [DEBUG] No positions found for auto BE/TP processing
2025-09-18 15:47:03,711 [INFO] \U0001f534 Webhook server STOPPED
2025-09-18 15:47:03,712 [INFO] \U0001f534 Webhook server stopped
2025-09-18 15:47:03,713 [INFO] \U0001f534 Stopping background processes...
2025-09-18 15:47:05,721 [INFO] \U0001f534 Stopping AI bot scheduler...
2025-09-18 15:47:07,731 [INFO] \U0001f534 MT5 connection closed
