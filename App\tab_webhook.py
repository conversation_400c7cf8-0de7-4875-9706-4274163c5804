
import MetaTrader5 as mt5
import customtkinter as ctk
import pandas as pd
import numpy as np
import talib as ta
import threading
import time
from datetime import datetime
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import gc
from flask import Flask, request, jsonify
from waitress import serve
import requests
import base64
import os
import json
import os
import random
import string
import socket
from contextlib import closing

# ===========================
# Class: TabWebhook
# ===========================
class TabWebhook:
    def __init__(self, master, config, util):
        self.frame = master.add("Webhook")
        self.config = config
        self.util = util
        self.webhook_enabled = True
        self.app = Flask(__name__)
        self.name = "WH"
        self.side_var = ctk.StringVar()
        self.symbol_var = ctk.StringVar()
        self.lot_var = ctk.DoubleVar()
        # self.point_bsl_var = ctk.IntVar(value=self.config.SL_POINTS)
        # self.point_btp_var = ctk.IntVar(value=self.config.TP1_POINTS)
        # self.point_ssl_var = ctk.IntVar(value=self.config.SL_POINTS)
        # self.point_stp_var = ctk.IntVar(value=self.config.TP1_POINTS)
        self.time_var = ctk.IntVar()
        self.loop_running = False
        self.auto_be_enabled = False

        # Server management attributes
        self.server = None
        self.server_thread = None
        self.server_running = False
        self.server_lock = threading.Lock()
        self.port = self.config.port_default

        self.form1 = ctk.CTkFrame(self.frame)
        self.form1.pack(pady=10)  

        self.build_ui()
        self.build_router()

        # Start webhook server by default (since switch is selected by default)
        self.start_webhook_server()
        

    def build_ui(self):
        self.lot_var.set(0.01)  # Default lot size
        self.lot_label = ctk.CTkLabel(self.form1, text="L Size:")
        self.lot_label.grid(row=1, column=0, padx=10, pady=5)
        self.lot_val = ctk.CTkEntry(self.form1, textvariable=self.lot_var)
        self.lot_val.grid(row=1, column=1, padx=10, pady=5)

        # self.symbol_label = ctk.CTkLabel(self.form1, text="Symbol:")
        # self.symbol_label.grid(row=1, column=2, padx=10, pady=5)
        # self.symbol_var.set("XU")  # Default symbol
        # self.symbol_dropdown = ctk.CTkOptionMenu(self.form1, values=list(self.config.symbols), variable=self.symbol_var)
        # self.symbol_dropdown.grid(row=1, column=3, padx=10, pady=5)
        
        # self.time_var.set(1)  # Default 5 min
        # self.time_label = ctk.CTkLabel(self.form1, text="Loop BE (min):")
        # self.time_label.grid(row=2, column=0, padx=10, pady=5)
        # self.time_val = ctk.CTkEntry(self.form1, textvariable=self.time_var)
        # self.time_val.grid(row=2, column=1, padx=10, pady=5)

        self.side_label = ctk.CTkLabel(self.form1, text="Side:")
        self.side_label.grid(row=1, column=2, padx=10, pady=5)
        self.side_var.set("BUY")  # Default symbol
        self.side_dropdown = ctk.CTkOptionMenu(self.form1, values=["BUY","SELL","BOTH"], variable=self.side_var)
        self.side_dropdown.grid(row=1, column=3, padx=10, pady=5)
  
        # self.status_label = ctk.CTkLabel(self.form3, text="🔘 Not Monitoring")
        # self.status_label.pack(pady=1) 

        self.switch_webhook = ctk.CTkSwitch(self.frame, text="Enable Webhook", command=self.toggle_webhook)
        self.switch_webhook.pack(padx=10, pady=10)
        self.switch_webhook.select()

    # Webhook Callback function
    def toggle_webhook(self):
        """Toggle webhook server on/off"""
        if self.switch_webhook.get():
            self.start_webhook_server()
        else:
            self.stop_webhook_server()

    def find_free_port(self, start_port=5000, max_attempts=10):
        """Find a free port starting from start_port"""
        for port in range(start_port, start_port + max_attempts):
            with closing(socket.socket(socket.AF_INET, socket.SOCK_STREAM)) as sock:
                try:
                    sock.bind(('', port))
                    return port
                except OSError:
                    continue
        return None

    def start_webhook_server(self):
        """Start the webhook server in a background thread"""
        with self.server_lock:
            if self.server_running:
                self.util.add_status_frame("🟡 Webhook server already running", "yellow")
                return

            # Find a free port
            # free_port = self.find_free_port(self.port)
            # if free_port is None:
            #     self.util.add_status_frame("❌ No free ports available for webhook server", "red")
            #     self.switch_webhook.deselect()
            #     return

            # self.port = free_port

            try:
                # Start server in background thread
                self.server_thread = threading.Thread(target=self._run_server, daemon=True)
                self.server_thread.start()

                # Wait a moment to ensure server starts
                time.sleep(0.5)

                if self.server_running:
                    self.webhook_enabled = True
                    self.util.add_status_frame(f"🟢 Webhook server STARTED on port {self.port}", "green")
                else:
                    self.util.add_status_frame("❌ Failed to start webhook server", "red")
                    self.switch_webhook.deselect()

            except Exception as e:
                self.util.add_status_frame(f"❌ Error starting webhook server: {str(e)}", "red")
                self.switch_webhook.deselect()

    def stop_webhook_server(self):
        """Stop the webhook server"""
        with self.server_lock:
            if not self.server_running:
                self.util.add_status_frame("🟡 Webhook server already stopped", "yellow")
                return

            try:
                self.webhook_enabled = False
                self.server_running = False

                # Give server time to stop gracefully
                if self.server_thread and self.server_thread.is_alive():
                    self.server_thread.join(timeout=2.0)

                self.util.add_status_frame("🔴 Webhook server STOPPED", "red")

            except Exception as e:
                self.util.add_status_frame(f"❌ Error stopping webhook server: {str(e)}", "red")

    def _run_server(self):
        """Internal method to run the Flask server"""
        try:
            self.server_running = True
            serve(self.app, host="0.0.0.0", port=self.port, threads=4)
        except Exception as e:
            self.util.add_status_frame(f"❌ Webhook server error: {str(e)}", "red")
        finally:
            self.server_running = False

    def run_webhook(self):
        """Legacy method - now starts the server properly"""
        self.start_webhook_server()

    def cleanup(self):
        """Cleanup method to be called when application closes"""
        try:
            self.stop_webhook_server()
        except Exception as e:
            print(f"Error during webhook cleanup: {e}")

    def safe_ui_update(self, message, color="white", level=3):
        """Thread-safe method to update UI from webhook threads"""
        try:
            # Schedule UI update on main thread
            if hasattr(self.util, 'add_status_frame'):
                # Use after_idle to ensure UI update happens on main thread
                self.util.add_status_frame(message, color, level)
        except Exception as e:
            print(f"Error updating UI: {e}")

    def safe_send_order(self, action, symbol, lot, price, sl, tp, comment, reason="", risk="", signal_id=""):
        """Thread-safe wrapper for sending orders"""
        try:
            return self.util.send_order(action, symbol, lot, price, sl, tp, comment, reason, risk, signal_id)
        except Exception as e:
            self.safe_ui_update(f"❌ Order error: {str(e)}", "red")
            return None

    def verify_access_token(self):
        """
        Verify access token from both Authorization header (Bearer token) and X-Access-Token header
        Returns tuple: (is_valid, error_message)
        """
        # Get the expected access token from environment
        expected_token = os.getenv('WEBHOOK_ACCESS_TOKEN', 'ec2a9c0db08fdda7ca38f346ebf34eb0ab3a8ff918db3a9d9fed2a71f68865a8')

        if not expected_token or expected_token == '':
            return False, f"Webhook access token not configured."

        # Check Authorization header (Bearer token)
        auth_header = request.headers.get('Authorization', '')
        bearer_token = None
        if auth_header.startswith('Bearer '):
            bearer_token = auth_header[7:]  # Remove 'Bearer ' prefix

        # Check X-Access-Token header
        x_access_token = request.headers.get('X-Access-Token', '')

        # Verify both tokens match the expected token
        if bearer_token == expected_token and x_access_token == expected_token:
            return True, None

        # Log the verification failure details
        error_details = []
        if not bearer_token:
            error_details.append("Missing Authorization Bearer token")
        elif bearer_token != expected_token:
            error_details.append("Invalid Authorization Bearer token")

        if not x_access_token:
            error_details.append("Missing X-Access-Token header")
        elif x_access_token != expected_token:
            error_details.append("Invalid X-Access-Token header")

        return False, "; ".join(error_details)

    def generate_input_id(self):
        """Generate random INPUT_ID with 8 characters (lowercase a-z + 0-9)"""
        characters = string.ascii_lowercase + string.digits
        return ''.join(random.choice(characters) for _ in range(8))

    def build_router(self):
 
        @self.app.route('/webhook_input', methods=['POST'])
        def webhook_input():
            try:
                if not self.webhook_enabled:
                    self.safe_ui_update(f"❌ Webhook failed: Webhook disabled - " + request.get_data(as_text=True), "red")
                    return jsonify({"error": True, "message": "Webhook disabled"}), 200

                # Verify access token
                is_valid, error_message = self.verify_access_token()
                if not is_valid:
                    self.safe_ui_update(f"❌ Webhook failed: Access token verification failed - {error_message}", "red")
                    return jsonify({"error": True, "message": f"Access denied: {error_message}"}), 401

                data = request.get_json(silent=True, force=True)
                if not data:
                    self.safe_ui_update(f"❌ Webhook failed: Invalid data, not JSON - " + request.get_data(as_text=True), "red")
                    return jsonify({"error": True, "message": "Invalid data, not JSON"}), 200

                s ,a, p, comment = data.get("s"), data.get("a"), data.get("p"), data.get("c")
                rows ,vsl, id, reason, risk = data.get("rows"), data.get("sl"), data.get("id"), data.get("reason"), data.get("risk", "")

                if not s or not a or not p:
                    self.safe_ui_update(f"❌ Webhook failed: Missing symbol or action or price - s:{s} a:{a} p:{p} c:{comment}", "red")
                    return jsonify({"error": True, "message": "Missing symbol or action or price"}), 200

                self.safe_ui_update(f"✅ Webhook Input: s:{s} a:{a} p:{p} c:{comment} :: {data}", "green")

                # Collect TP and Lot values
                symbol = s + self.config.symbol_posfix

                # Get price safely
                try:
                    tick_info = mt5.symbol_info_tick(symbol)
                    if tick_info is None:
                        self.safe_ui_update(f"❌ Failed to get tick info for symbol: {symbol}", "red")
                        return jsonify({"error": True, "message": f"Invalid symbol: {symbol}"}), 200

                    price = tick_info.ask if a == "Buy Now" else tick_info.bid if a == "Sell Now" else p
                except Exception as e:
                    self.safe_ui_update(f"❌ Error getting price for {symbol}: {str(e)}", "red")
                    return jsonify({"error": True, "message": f"Price error: {str(e)}"}), 200

                # Process orders
                orders_sent = 0
                i = 0
                for row in rows:
                    i += 1
                    try:
                        tp = float(row['tp'])
                        lot = float(row['lot'])
                        if tp and lot:
                            result = self.safe_send_order(a, symbol, lot, price, vsl, tp, comment + f"_{i}_{id}", reason, risk, id)
                            # result = self.safe_send_order(a, symbol, lot, price, vsl, tp, comment + f"_TP{i}_{id}")
                            if result:
                                orders_sent += 1
                    except Exception as e:
                        self.safe_ui_update(f"❌ Error processing order {i}: {str(e)}", "red")

                # Log the result
                self.safe_ui_update(f"✅ Webhook Input: {orders_sent}/{len(rows)} orders sent with ID: {id}", "green")

                return jsonify({"error": False, "message": f"Success: {orders_sent}/{len(rows)} orders sent"}), 200

            except Exception as e:
                self.safe_ui_update(f"❌ Webhook Input error: {str(e)}", "red")
                return jsonify({"error": True, "message": f"Server error: {str(e)}"}), 500
 
        @self.app.route('/webhook_instant', methods=['POST'])
        def webhook_instant():
            if not self.webhook_enabled:
                self.util.add_status_frame(f"❌ Webhook failed: Webhook disabled - " + request.get_data(as_text=True))
                return jsonify({"error": True, "message": "Webhook disabled"}), 200

            # Verify access token
            is_valid, error_message = self.verify_access_token()
            if not is_valid:
                self.util.add_status_frame(f"❌ Webhook failed: Access token verification failed - {error_message}")
                return jsonify({"error": True, "message": f"Access denied: {error_message}"}), 401

            data = request.get_json(silent=True, force=True)
            if not data:
                self.util.add_status_frame(f"❌ Webhook failed: Invalid data, not JSON - " + request.get_data(as_text=True))
                return jsonify({"error": True, "message": "Invalid data, not JSON"}), 200
            
            s ,a, p,     = data.get("s"), data.get("a"), data.get("p")
            comment, lot = data.get("c"), data.get("lot")
            ptp ,psl     =  data.get("ptp"),  data.get("psl")
            vtp ,vsl     =  data.get("tp"),  data.get("sl")
            reason       = data.get("reason", "")  # Optional reason field
            signal_id    = data.get("signal_id", "")  # Optional signal ID field
            risk         = data.get("risk", "")  # Optional risk field

            if not s or not a:
                self.util.add_status_frame(f"❌ Webhook failed: Missing symbol or action or price - s:{s} a:{a} c:{comment}")
                return jsonify({"error": True, "message": "Missing symbol or action or price"}), 200
            
            self.util.add_status_frame(f"✅ Webhook Instant: s:{s} a:{a} c:{comment} :: {data}")

            # symbol = self.config.symbols[s] + self.config.symbol_posfix
            symbol = s + self.config.symbol_posfix
            lot = self.lot_var.get() if not lot else lot
            point = mt5.symbol_info(symbol).point
            
            entry = mt5.symbol_info_tick(symbol).ask if a == "Buy Now" else (mt5.symbol_info_tick(symbol).bid if a == "Sell Now" else p )
            if (a in ["Buy Limit", "Buy Now", "Buy Stop"]) and (self.side_var.get() == "BUY" or self.side_var.get() == "BOTH"):
                    tp = (entry + (point*ptp)) if not vtp else vtp
                    sl = (entry - (point*psl)) if not vsl else vsl
                    # tp = (entry + (stp*xtp)) if not vtp else vtp
                    # sl = (entry - (stp*xsl)) if not vsl else vsl
                    self.util.send_order(a, symbol, lot, entry, sl, tp, comment, reason, risk, signal_id)
                    return jsonify({"error": False, "message": "Success"}), 200
            elif (a in ["Sell Limit", "Sell Now", "Sell Stop"]) and (self.side_var.get() == "Sell" or self.side_var.get() == "BOTH"):
                    tp = (entry - (point*ptp)) if not vtp else vtp
                    sl = (entry + (point*psl)) if not vsl else vsl
                    # tp = (entry - (stp*xtp)) if not vtp else vtp
                    # sl = (entry + (stp*xsl)) if not vsl else vsl
                    self.util.send_order(a, symbol, lot, entry, sl, tp, comment, reason, risk, signal_id)
                    return jsonify({"error": False, "message": "Success"}), 200
                     
            return jsonify({"error": True, "message": "Only accept " + self.side_var.get()}), 200 
        
        @self.app.route('/webhook_control', methods=['POST'])
        def webhook_control():
            if not self.webhook_enabled:
                self.util.add_status_frame(f"❌ Webhook failed: Webhook disabled - " + request.get_data(as_text=True))
                # return "Webhook disabled", 403
                return "Webhook disabled", 200

            # Verify access token
            is_valid, error_message = self.verify_access_token()
            if not is_valid:
                self.util.add_status_frame(f"❌ Webhook failed: Access token verification failed - {error_message}")
                return jsonify({"error": True, "message": f"Access denied: {error_message}"}), 401

            data = request.get_json(silent=True, force=True)
            if not data:
                self.util.add_status_frame(f"❌ Webhook failed: Invalid data, not JSON - " + request.get_data(as_text=True))
                return jsonify({"error": True, "message": "Invalid data, not JSON "}), 400
            
            s ,a, condition = data.get("s"), data.get("a"), data.get("c")
            filter_comment = data.get("filter", "")  # Optional filter parameter
            symbol = s + self.config.symbol_posfix

            if a == "close":
                # condition = all, all-buy, all-sell | all-profit, buy-profit, sell-profit | all-loss, buy-loss, sell-loss
                self.util.close_orders_by_condition(symbol, condition)
            elif a == "close-pending":
                # Close pending orders (limit and stop orders) that are not yet active
                # filter_comment: if empty, closes all pending orders; if provided, closes orders where comment ends with filter_comment
                closed_count = self.util.close_pending_orders_by_filter(symbol, filter_comment)
                self.util.add_status_frame(f"✅ Webhook Control: Cancelled {closed_count} pending orders for {symbol} with filter '{filter_comment}'")
            elif a == "sl2be":
                # condition = all, all-buy, all-sell
                self.util.update_SL_to_BE_by_condition(symbol, condition)
            elif a == "tp2be":
                # condition = all, all-buy, all-sell
                self.util.update_TP_to_BE_by_condition(symbol, condition)
            elif a == "trailing-sl":
                # condition = all, buy, sell
                # Manually trigger trailing stop loss calculation
                result = self.util.set_trailing_sl(symbol, condition)
                if result:
                    processed_count = result.get("All_FT", 0)
                    self.util.add_status_frame(f"✅ Webhook Control: Trailing SL applied to {processed_count} {condition.upper()} positions for {symbol}")
                else:
                    self.util.add_status_frame(f"⚠️ Webhook Control: No positions processed for trailing SL on {symbol} ({condition.upper()})")

            # return "OK", 200
            return jsonify({"error": False, "message": "Success"}), 200

        @self.app.route('/webhook_orders_data', methods=['GET'])
        def webhook_orders_data():
            """Get orders data for web app display"""
            if not self.webhook_enabled:
                return jsonify({"error": True, "message": "Webhook disabled"}), 200

            # Verify access token
            is_valid, error_message = self.verify_access_token()
            if not is_valid:
                self.util.add_status_frame(f"❌ Webhook failed: Access token verification failed - {error_message}")
                return jsonify({"error": True, "message": f"Access denied: {error_message}"}), 401

            try:
                # Get positions data
                positions_data = []
                positions = mt5.positions_get() if self.util.initialize_mt5() else []
                if positions:
                    for pos in positions:
                        positions_data.append({
                            "ticket": pos.ticket,
                            "symbol": pos.symbol,
                            "type": "BUY" if pos.type == 0 else "SELL",
                            "volume": pos.volume,
                            "entry": pos.price_open,
                            "current": pos.price_current,
                            "sl": pos.sl if pos.sl > 0 else None,
                            "tp": pos.tp if pos.tp > 0 else None,
                            "profit": pos.profit,
                            "magic": pos.magic,
                            "comment": pos.comment
                        })

                # Get pending orders data
                pending_data = []
                orders = mt5.orders_get() if self.util.initialize_mt5() else []
                if orders:
                    for order in orders:
                        order_type_name = "Buy Limit" if order.type == mt5.ORDER_TYPE_BUY_LIMIT else \
                                         "Sell Limit" if order.type == mt5.ORDER_TYPE_SELL_LIMIT else \
                                         "Buy Stop" if order.type == mt5.ORDER_TYPE_BUY_STOP else \
                                         "Sell Stop" if order.type == mt5.ORDER_TYPE_SELL_STOP else \
                                         f"Type {order.type}"

                        pending_data.append({
                            "ticket": order.ticket,
                            "symbol": order.symbol,
                            "type": order_type_name,
                            "volume": order.volume_initial,
                            "entry": order.price_open,
                            "current": order.price_current,
                            "sl": order.sl if order.sl > 0 else None,
                            "tp": order.tp if order.tp > 0 else None,
                            "magic": order.magic,
                            "comment": order.comment
                        })

                # Get grouped data
                zd_groups = self.get_grouped_orders("ZD")
                in_groups = self.get_grouped_orders("IN")

                return jsonify({
                    "error": False,
                    "data": {
                        "positions": positions_data,
                        "pending": pending_data,
                        "zd_groups": zd_groups,
                        "in_groups": in_groups,
                        "timestamp": time.time()
                    }
                }), 200

            except Exception as e:
                self.util.add_status_frame(f"❌ Orders data error: {e}", "red")
                return jsonify({"error": True, "message": f"Data retrieval error: {str(e)}"}), 500

        @self.app.route('/webhook_orders_action', methods=['POST'])
        def webhook_orders_action():
            """Handle order actions from web app"""
            if not self.webhook_enabled:
                return jsonify({"error": True, "message": "Webhook disabled"}), 200

            # Verify access token
            is_valid, error_message = self.verify_access_token()
            if not is_valid:
                self.util.add_status_frame(f"❌ Webhook failed: Access token verification failed - {error_message}")
                return jsonify({"error": True, "message": f"Access denied: {error_message}"}), 401

            data = request.get_json(silent=True, force=True)
            if not data:
                self.util.add_status_frame(f"❌ Webhook failed: Invalid data, not JSON - " + request.get_data(as_text=True))
                return jsonify({"error": True, "message": "Invalid data, not JSON"}), 400

            action = data.get("action")
            group_type = data.get("group_type")  # "ZD" or "IN"
            group_id = data.get("group_id")
            auto_be_settings = data.get("auto_be_settings")  # For toggle auto SL to BE

            # New parameters for individual order operations
            ticket = data.get("ticket")
            order_type = data.get("orderType")  # 'position' or 'pending'

            try:
                if action == "close_group":
                    if group_type == "ZD" and group_id:
                        closed_count = self.close_group_by_id(group_id, "ZD")
                        self.util.add_status_frame(f"✅ Webhook: Closed ZD group {group_id}: {closed_count} orders")
                        return jsonify({"error": False, "message": f"Closed ZD group {group_id}: {closed_count} orders"}), 200
                    elif group_type == "IN" and group_id:
                        closed_count = self.close_group_by_id(group_id, "IN")
                        self.util.add_status_frame(f"✅ Webhook: Closed IN group {group_id}: {closed_count} orders")
                        return jsonify({"error": False, "message": f"Closed IN group {group_id}: {closed_count} orders"}), 200
                    else:
                        return jsonify({"error": True, "message": "Invalid group_type or missing group_id"}), 400

                elif action == "close_position":
                    if not ticket or order_type != "position":
                        return jsonify({"error": True, "message": "Missing ticket or invalid orderType for close_position"}), 400

                    result = self.close_position_by_ticket(ticket)
                    if result["success"]:
                        self.util.add_status_frame(f"✅ Webhook: Closed position {ticket}")
                        return jsonify({"error": False, "message": f"Position {ticket} closed successfully"}), 200
                    else:
                        self.util.add_status_frame(f"❌ Webhook: Failed to close position {ticket}: {result['message']}", "red")
                        return jsonify({"error": True, "message": result["message"]}), 400

                elif action == "cancel_order":
                    if not ticket or order_type != "pending":
                        return jsonify({"error": True, "message": "Missing ticket or invalid orderType for cancel_order"}), 400

                    result = self.cancel_order_by_ticket(ticket)
                    if result["success"]:
                        self.util.add_status_frame(f"✅ Webhook: Canceled pending order {ticket}")
                        return jsonify({"error": False, "message": f"Pending order {ticket} canceled successfully"}), 200
                    else:
                        self.util.add_status_frame(f"❌ Webhook: Failed to cancel order {ticket}: {result['message']}", "red")
                        return jsonify({"error": True, "message": result["message"]}), 400

                elif action == "toggle_auto_be":
                    if auto_be_settings:
                        # Update auto SL to BE settings
                        result = self.update_auto_be_settings(auto_be_settings)
                        return jsonify({"error": False, "message": "Auto SL to BE settings updated", "result": result}), 200
                    else:
                        return jsonify({"error": True, "message": "Missing auto_be_settings"}), 400

                else:
                    return jsonify({"error": True, "message": f"Unknown action: {action}"}), 400

            except Exception as e:
                self.util.add_status_frame(f"❌ Orders action error: {e}", "red")
                return jsonify({"error": True, "message": f"Action error: {str(e)}"}), 500

        @self.app.route('/webhook_chart_data', methods=['POST'])
        def webhook_chart_data():
            """Get chart data with technical indicators for AI analysis"""
            try:
                if not self.webhook_enabled:
                    self.safe_ui_update("❌ Webhook failed: Webhook disabled", "red")
                    return jsonify({"error": True, "message": "Webhook disabled"}), 200

                # Verify access token
                is_valid, error_message = self.verify_access_token()
                if not is_valid:
                    self.safe_ui_update(f"❌ Webhook failed: Access token verification failed - {error_message}", "red")
                    return jsonify({"error": True, "message": f"Access denied: {error_message}"}), 401

                data = request.get_json(silent=True, force=True)
                if not data:
                    self.safe_ui_update("❌ Webhook failed: Invalid data, not JSON", "red")
                    return jsonify({"error": True, "message": "Invalid data, not JSON"}), 400

                symbol = data.get("symbol")
                timeframes = data.get("timeframes")  # Can be list or single timeframe
                timeframe = data.get("timeframe", "H1")  # Fallback for single timeframe
                barback = data.get("barback", 100)  # Default to 100 bars

                # AI Bot specific parameters
                bot_id = data.get("bot_id")
                bot_name = data.get("bot_name")
                custom_prompt = data.get("custom_prompt", "")

                if not symbol:
                    self.safe_ui_update("❌ Webhook failed: Missing symbol parameter", "red", level=2)
                    return jsonify({"error": True, "message": "Missing symbol parameter"}), 400

                # Handle multi-timeframe or single timeframe
                if timeframes and isinstance(timeframes, list):
                    # Multi-timeframe request
                    target_timeframes = timeframes
                else:
                    # Single timeframe request (backward compatibility)
                    target_timeframes = [timeframe]

                # Add symbol postfix if configured
                full_symbol = symbol + (self.config.symbol_posfix if self.config.symbol_posfix else "")

                # Get chart data for all requested timeframes
                multi_timeframe_data = {}
                total_bars = 0

                for tf in target_timeframes:
                    chart_data = self.util.get_chart_data(full_symbol, tf, barback)

                    if not chart_data:
                        self.safe_ui_update(f"❌ Failed to get chart data for {full_symbol} {tf}", "red", level=2)
                        return jsonify({"error": True, "message": f"Failed to get chart data for {full_symbol} {tf}"}), 500

                    multi_timeframe_data[tf] = chart_data
                    total_bars += len(chart_data)
                    self.safe_ui_update(f"✅ Chart data: {symbol} {tf} ({len(chart_data)} bars)", "green", level=4)

                # Prepare response
                if len(target_timeframes) == 1:
                    # Single timeframe response (backward compatibility)
                    response_data = {
                        "error": False,
                        "message": "Chart data retrieved successfully",
                        "symbol": symbol,
                        "timeframe": target_timeframes[0],
                        "bars_count": len(multi_timeframe_data[target_timeframes[0]]),
                        "data": multi_timeframe_data[target_timeframes[0]]
                    }
                else:
                    # Multi-timeframe response
                    response_data = {
                        "error": False,
                        "message": "Multi-timeframe chart data retrieved successfully",
                        "symbol": symbol,
                        "timeframes": target_timeframes,
                        "total_bars": total_bars,
                        "data": multi_timeframe_data
                    }

                # Add AI bot information if provided
                if bot_id:
                    response_data["bot_id"] = bot_id
                if bot_name:
                    response_data["bot_name"] = bot_name
                if custom_prompt:
                    response_data["custom_prompt"] = custom_prompt
                    response_data["ai_analysis_ready"] = True

                return jsonify(response_data), 200

            except Exception as e:
                self.safe_ui_update(f"❌ Chart data error: {str(e)}", "red")
                return jsonify({"error": True, "message": f"Chart data error: {str(e)}"}), 500

        @self.app.route('/webhook_ai_analysis', methods=['POST'])
        def webhook_ai_analysis():
            """Direct AI analysis webhook endpoint"""
            if not self.webhook_enabled:
                # self.safe_ui_update("❌ AI Analysis webhook failed: Webhook disabled", "red", level=2)
                return jsonify({"error": True, "message": "Webhook disabled"}), 200

            # Verify access token
            is_valid, error_message = self.verify_access_token()
            if not is_valid:
                # self.safe_ui_update(f"❌ AI Analysis webhook failed: Access token verification failed - {error_message}", "red", level=2)
                return jsonify({"error": True, "message": f"Access denied: {error_message}"}), 401

            data = request.get_json()
            if not data:
                self.safe_ui_update("❌ AI Analysis webhook failed: No JSON data", "red", level=2)
                return jsonify({"error": True, "message": "No JSON data provided"}), 400
            
            custom_prompt = data.get("prompt", "")
            if custom_prompt == "Test connection":
                self.safe_ui_update("🔍 Debug: Test connection: OK", "yellow", level=3)
                return jsonify({"error": False, "message": "Test connection: OK"}), 200

            try:
                # Extract parameters
                symbol = data.get("symbol")
                timeframes = data.get("timeframes", [])
                timeframe = data.get("timeframe", "H1")  # Fallback for single timeframe
                barback = data.get("barback", 100)
                ai_provider = data.get("ai", "gpt").lower()
                image_url = data.get("image", "")
                use_signal_format = data.get("use_signal_format", True)

                if not symbol:
                    self.safe_ui_update("❌ AI Analysis failed: Missing symbol parameter", "red", level=2)
                    return jsonify({"error": True, "message": "Missing symbol parameter"}), 400

                symbol = self.util.get_symbol(symbol)

                # Handle multi-timeframe or single timeframe
                if timeframes and isinstance(timeframes, list):
                    target_timeframes = timeframes
                else:
                    target_timeframes = [timeframe]

                self.safe_ui_update(f"🤖 AI Analysis started: {symbol} {target_timeframes} via {ai_provider.upper()}", "cyan", level=3)

                # Get chart data for all timeframes
                self.safe_ui_update(f"🔍 Attempting chart data for: {symbol} {target_timeframes} ({barback} bars)", "cyan", level=4)

                chart_data_result = self.util.get_multi_timeframe_data(symbol, target_timeframes, barback)

                if not chart_data_result:
                    error_msg = f"Failed to retrieve chart data for {symbol} {target_timeframes}. Check if symbol exists in MT5 and is spelled correctly."
                    self.safe_ui_update(f"❌ {error_msg}", "red", level=2)

                    # Add debug info about what was tried
                    self.safe_ui_update(f"🔍 Debug: Tried variations like {symbol}.iux, GOLD.iux, etc.", "yellow", level=3)
                    return jsonify({"error": True, "message": error_msg}), 400
                else:
                    actual_symbol = chart_data_result.get('actual_symbol', symbol)
                    total_bars = chart_data_result.get('total_bars', 0)
                    self.safe_ui_update(f"✅ Chart data retrieved: {actual_symbol} ({total_bars} bars)", "green", level=4)

                # Perform AI analysis
                analysis_result = self.util.perform_ai_analysis(
                    chart_data_result,
                    custom_prompt,
                    ai_provider,
                    image_url,
                    symbol,
                    target_timeframes,
                    use_signal_format
                )

                if analysis_result.get("error"):
                    error_msg = analysis_result.get("message", "AI analysis failed")
                    self.safe_ui_update(f"❌ AI Analysis: {error_msg}", "red", level=2)
                    return jsonify(analysis_result), 400

                self.safe_ui_update(f"✅ AI Analysis completed: {symbol} via {ai_provider.upper()}", "green", level=3)
                return jsonify(analysis_result), 200

            except Exception as e:
                error_msg = f"AI Analysis webhook error: {str(e)}"
                self.safe_ui_update(f"❌ {error_msg}", "red", level=1)
                return jsonify({"error": True, "message": f"Server error: {str(e)}"}), 500



        @self.app.route('/webhook_ai_bot_schedule', methods=['GET'])
        def webhook_ai_bot_schedule_status():
            """Get AI bot schedule status"""
            try:
                if not self.webhook_enabled:
                    return jsonify({"error": True, "message": "Webhook disabled"}), 200

                # Verify access token
                is_valid, error_message = self.verify_access_token()
                if not is_valid:
                    self.safe_ui_update(f"❌ AI bot schedule status failed: Access token verification failed - {error_message}", "red")
                    return jsonify({"error": True, "message": f"Access denied: {error_message}"}), 401

                # Get AI bot schedule status
                if hasattr(self.config, 'ai_bots'):
                    bot_schedules = {}
                    for bot_id, bot_config in self.config.ai_bots.items():
                        bot_schedules[bot_id] = {
                            "name": bot_config.get("name", f"Bot {bot_id}"),
                            "enabled": bot_config.get("enabled", True),
                            "schedule_check": bot_config.get("schedule_check", False),
                            "schedule_type": bot_config.get("schedule_type", "custom"),
                            "schedule_custom_minutes": bot_config.get("schedule_custom_minutes", 60),
                            "schedule_daily_time": bot_config.get("schedule_daily_time", "09:00"),
                            "schedule_weekly_day": bot_config.get("schedule_weekly_day", "Monday"),
                            "schedule_weekly_time": bot_config.get("schedule_weekly_time", "09:00"),
                            "schedule_monthly_day": bot_config.get("schedule_monthly_day", 1),
                            "schedule_monthly_time": bot_config.get("schedule_monthly_time", "09:00"),
                            "auto_place_order": bot_config.get("auto_place_order", False),
                            "last_used": bot_config.get("last_used"),
                            "symbol": bot_config.get("symbol"),
                            "timeframes": bot_config.get("timeframes", [])
                        }

                    return jsonify({"error": False, "bot_schedules": bot_schedules}), 200

                return jsonify({"error": True, "message": "AI bots not available"}), 200

            except Exception as e:
                self.safe_ui_update(f"❌ AI bot schedule status error: {str(e)}", "red")
                return jsonify({"error": True, "message": f"AI bot schedule status error: {str(e)}"}), 500

        @self.app.route('/webhook_ai_bot_schedule', methods=['POST'])
        def webhook_ai_bot_schedule_settings():
            """Update AI bot schedule settings"""
            try:
                if not self.webhook_enabled:
                    self.safe_ui_update("❌ Webhook failed: Webhook disabled", "red")
                    return jsonify({"error": True, "message": "Webhook disabled"}), 200

                # Verify access token
                is_valid, error_message = self.verify_access_token()
                if not is_valid:
                    self.safe_ui_update(f"❌ AI bot schedule settings failed: Access token verification failed - {error_message}", "red")
                    return jsonify({"error": True, "message": f"Access denied: {error_message}"}), 401

                data = request.get_json(silent=True, force=True)
                if not data:
                    self.safe_ui_update("❌ AI bot schedule settings failed: Invalid data, not JSON", "red")
                    return jsonify({"error": True, "message": "Invalid data, not JSON"}), 200

                bot_id = data.get("bot_id")
                if not bot_id:
                    return jsonify({"error": True, "message": "bot_id is required"}), 200

                if bot_id not in self.config.ai_bots:
                    return jsonify({"error": True, "message": f"Bot {bot_id} not found"}), 200

                # Update bot configuration
                bot_config = self.config.ai_bots[bot_id].copy()

                if "schedule_check" in data:
                    bot_config["schedule_check"] = data["schedule_check"]
                if "schedule_type" in data:
                    bot_config["schedule_type"] = data["schedule_type"]
                if "schedule_custom_minutes" in data:
                    bot_config["schedule_custom_minutes"] = data["schedule_custom_minutes"]
                if "schedule_daily_time" in data:
                    bot_config["schedule_daily_time"] = data["schedule_daily_time"]
                if "schedule_weekly_day" in data:
                    bot_config["schedule_weekly_day"] = data["schedule_weekly_day"]
                if "schedule_weekly_time" in data:
                    bot_config["schedule_weekly_time"] = data["schedule_weekly_time"]
                if "schedule_monthly_day" in data:
                    bot_config["schedule_monthly_day"] = data["schedule_monthly_day"]
                if "schedule_monthly_time" in data:
                    bot_config["schedule_monthly_time"] = data["schedule_monthly_time"]
                if "auto_place_order" in data:
                    bot_config["auto_place_order"] = data["auto_place_order"]
                if "enabled" in data:
                    bot_config["enabled"] = data["enabled"]

                # Save the updated configuration
                self.config.ai_bots[bot_id] = bot_config
                self.config.save_ai_bots()

                bot_name = bot_config.get("name", f"Bot {bot_id}")
                self.safe_ui_update(f"✅ AI bot schedule updated via webhook: {bot_name}", "green")

                return jsonify({"error": False, "message": f"Bot {bot_id} schedule updated successfully"}), 200

            except Exception as e:
                self.safe_ui_update(f"❌ AI bot schedule settings error: {str(e)}", "red")
                return jsonify({"error": True, "message": f"AI bot schedule settings error: {str(e)}"}), 500

    def extract_zd_id(self, comment):
        """Extract ID from comment like 'ZD_XXX_X_X_<ID>'"""
        if not comment.startswith('ZD'):
            return None
        parts = comment.split('_')
        # if len(parts) >= 5:
        #     return parts[-1]
        if len(parts[-1]) >= 5:  # ZD_XXX_X_X_<ID>
            return parts[-1]  # Return the last part as ID
        return None

    def extract_input_id(self, comment):
        """Extract ID from comment like 'IN_XXX_X_X_<ID>'"""
        if not comment.startswith('IN'):
            return None
        parts = comment.split('_')
        # if len(parts) >= 3:
        #     for part in parts:
        #         if len(part) == 8 and part.islower() and any(c.isdigit() for c in part):
        #             return part 
        if len(parts[-1]) >= 5:  # INPUT_XXX_X_X_<ID>
            return parts[-1]  # Return the last part as ID
        return None

    def get_grouped_orders(self, group_type):
        """Get grouped orders data for ZD or IN"""
        from collections import defaultdict

        groups = defaultdict(lambda: {'positions': [], 'pending': [], 'tp_count': 0, 'total_volume': 0, 'total_profit': 0})

        # Process positions
        positions = mt5.positions_get() if self.util.initialize_mt5() else []
        if positions:
            for pos in positions:
                if group_type == "ZD":
                    group_id = self.extract_zd_id(pos.comment)
                else:  # INPUT
                    group_id = self.extract_input_id(pos.comment)

                if group_id:
                    groups[group_id]['positions'].append({
                        "ticket": pos.ticket,
                        "symbol": pos.symbol,
                        "type": "BUY" if pos.type == 0 else "SELL",
                        "volume": pos.volume,
                        "entry": pos.price_open,
                        "current": pos.price_current,
                        "sl": pos.sl if pos.sl > 0 else None,
                        "tp": pos.tp if pos.tp > 0 else None,
                        "profit": pos.profit,
                        "magic": pos.magic,
                        "comment": pos.comment
                    })
                    groups[group_id]['total_volume'] += pos.volume
                    groups[group_id]['total_profit'] += pos.profit
                    # if '_TP' in pos.comment:
                    groups[group_id]['tp_count'] += 1

        # Process pending orders
        orders = mt5.orders_get() if self.util.initialize_mt5() else []
        if orders:
            for order in orders:
                if group_type == "ZD":
                    group_id = self.extract_zd_id(order.comment)
                else:  # INPUT
                    group_id = self.extract_input_id(order.comment)

                if group_id:
                    order_type_name = "Buy Limit" if order.type == mt5.ORDER_TYPE_BUY_LIMIT else \
                                     "Sell Limit" if order.type == mt5.ORDER_TYPE_SELL_LIMIT else \
                                     "Buy Stop" if order.type == mt5.ORDER_TYPE_BUY_STOP else \
                                     "Sell Stop" if order.type == mt5.ORDER_TYPE_SELL_STOP else \
                                     f"Type {order.type}"

                    groups[group_id]['pending'].append({
                        "ticket": order.ticket,
                        "symbol": order.symbol,
                        "type": order_type_name,
                        "volume": order.volume_initial,
                        "entry": order.price_open,
                        "current": order.price_current,
                        "sl": order.sl if order.sl > 0 else None,
                        "tp": order.tp if order.tp > 0 else None,
                        # "magic": order.magic,
                        "comment": order.comment
                    })
                    groups[group_id]['total_volume'] += order.volume_initial
                    # if '_TP' in order.comment:
                    groups[group_id]['tp_count'] += 1

        # Convert to list format
        result = []
        for group_id, data in groups.items():
            result.append({
                "group_id": group_id,
                "tp_count": data['tp_count'],
                "total_volume": data['total_volume'],
                "total_profit": data['total_profit'],
                "positions_count": len(data['positions']),
                "pending_count": len(data['pending']),
                "positions": data['positions'],
                # "magic": data['magic'],
                "pending": data['pending']
            })

        return result

    def close_group_by_id(self, group_id, group_type):
        """Close all orders in a group by ID"""
        closed_count = 0

        # Close positions
        positions = mt5.positions_get() if self.util.initialize_mt5() else []
        if positions:
            for pos in positions:
                should_close = False
                if group_type == "ZD" and pos.comment.endswith(group_id):
                    should_close = True
                elif group_type == "IN" and group_id in pos.comment and pos.comment.startswith('IN'):
                    should_close = True

                if should_close:
                    price = mt5.symbol_info_tick(pos.symbol).bid if pos.type == 0 else mt5.symbol_info_tick(pos.symbol).ask
                    order_type = mt5.ORDER_TYPE_SELL if pos.type == 0 else mt5.ORDER_TYPE_BUY
                    close_request = {
                        "action": mt5.TRADE_ACTION_DEAL,
                        "position": pos.ticket,
                        "symbol": pos.symbol,
                        "volume": pos.volume,
                        "type": order_type,
                        "price": price,
                        "deviation": 10,
                        "magic": 155214,
                        "comment": f"Close {group_type} group {group_id}",
                        "type_filling": mt5.ORDER_FILLING_FOK,
                    }
                    result = mt5.order_send(close_request)
                    if result.retcode == mt5.TRADE_RETCODE_DONE:
                        closed_count += 1

        # Close pending orders
        orders = mt5.orders_get() if self.util.initialize_mt5() else []
        if orders:
            for order in orders:
                should_close = False
                if group_type == "ZD" and order.comment.endswith(group_id):
                    should_close = True
                elif group_type == "IN" and group_id in order.comment and order.comment.startswith('IN'):
                    should_close = True

                if should_close:
                    cancel_request = {
                        "action": mt5.TRADE_ACTION_REMOVE,
                        "order": order.ticket,
                    }
                    result = mt5.order_send(cancel_request)
                    if result.retcode == mt5.TRADE_RETCODE_DONE:
                        closed_count += 1

        return closed_count

    def update_auto_be_settings(self, settings):
        """Update auto SL to BE settings (placeholder for future integration)"""
        # This would integrate with TabOrders auto BE settings
        # For now, just return the settings received
        self.util.add_status_frame(f"🔄 Webhook: Auto SL to BE settings updated: {settings}")
        return {
            "updated": True,
            "settings": settings,
            "message": "Auto SL to BE settings updated via webhook"
        }

    def close_position_by_ticket(self, ticket):
        """Close a specific position by ticket number"""
        try:
            if not self.util.initialize_mt5():
                return {"success": False, "message": "MT5 initialization failed"}

            # Get the specific position
            positions = mt5.positions_get(ticket=ticket)
            if not positions or len(positions) == 0:
                return {"success": False, "message": f"Position {ticket} not found"}

            pos = positions[0]

            # Get current price for closing
            symbol_info = mt5.symbol_info_tick(pos.symbol)
            if not symbol_info:
                return {"success": False, "message": f"Failed to get price for {pos.symbol}"}

            # Determine close price and order type
            price = symbol_info.bid if pos.type == 0 else symbol_info.ask  # BUY positions close at bid, SELL at ask
            order_type = mt5.ORDER_TYPE_SELL if pos.type == 0 else mt5.ORDER_TYPE_BUY

            # Create close request
            close_request = {
                "action": mt5.TRADE_ACTION_DEAL,
                "position": pos.ticket,
                "symbol": pos.symbol,
                "volume": pos.volume,
                "type": order_type,
                "price": price,
                "deviation": 10,
                "magic": 155214,
                "comment": f"Webhook close position {ticket}",
                "type_filling": mt5.ORDER_FILLING_FOK,
            }

            # Send close order
            result = mt5.order_send(close_request)
            if result.retcode == mt5.TRADE_RETCODE_DONE:
                return {"success": True, "message": f"Position {ticket} closed successfully"}
            else:
                return {"success": False, "message": f"Failed to close position: {result.comment}"}

        except Exception as e:
            return {"success": False, "message": f"Error closing position: {str(e)}"}

    def cancel_order_by_ticket(self, ticket):
        """Cancel a specific pending order by ticket number"""
        try:
            if not self.util.initialize_mt5():
                return {"success": False, "message": "MT5 initialization failed"}

            # Get the specific order
            orders = mt5.orders_get(ticket=ticket)
            if not orders or len(orders) == 0:
                return {"success": False, "message": f"Pending order {ticket} not found"}

            # Create cancel request
            cancel_request = {
                "action": mt5.TRADE_ACTION_REMOVE,
                "order": ticket,
            }

            # Send cancel order
            result = mt5.order_send(cancel_request)
            if result.retcode == mt5.TRADE_RETCODE_DONE:
                return {"success": True, "message": f"Pending order {ticket} canceled successfully"}
            else:
                return {"success": False, "message": f"Failed to cancel order: {result.comment}"}

        except Exception as e:
            return {"success": False, "message": f"Error canceling order: {str(e)}"}
