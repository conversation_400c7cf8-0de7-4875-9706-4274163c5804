2025-09-18 15:33:11,076 - INFO - log_info:142 - 🟢 Orders processing loop started
2025-09-18 15:33:11,130 - DEBUG - debug_print:154 - No positions found for auto BE/TP processing
2025-09-18 15:33:43,295 - DEBUG - debug_print:154 - No positions found for auto BE/TP processing
2025-09-18 15:34:13,366 - DEBUG - debug_print:154 - No positions found for auto BE/TP processing
2025-09-18 15:34:43,444 - DEBUG - debug_print:154 - No positions found for auto BE/TP processing
2025-09-18 15:35:13,531 - DEBUG - debug_print:154 - No positions found for auto BE/TP processing
2025-09-18 15:37:42,738 - INFO - log_info:142 - 🟢 Orders processing loop started
2025-09-18 15:37:42,792 - DEBUG - debug_print:154 - No positions found for auto BE/TP processing
2025-09-18 15:38:14,611 - DEBUG - debug_print:154 - No positions found for auto BE/TP processing
2025-09-18 15:38:44,684 - DEBUG - debug_print:154 - No positions found for auto BE/TP processing
2025-09-18 15:39:14,746 - DEBUG - debug_print:154 - No positions found for auto BE/TP processing
2025-09-18 15:39:44,825 - DEBUG - debug_print:154 - Processing auto BE/TP for 1 symbols: {'GBPUSD.iux'}
2025-09-18 15:39:44,825 - DEBUG - debug_print:154 - Processing 2 positions for GBPUSD.iux
2025-09-18 15:39:44,827 - DEBUG - debug_print:154 - Processing 2 positions for  (SL: True, TP: True)
2025-09-18 15:39:44,865 - DEBUG - log_magic_number_info:110 - DEBUG: Magic number usage - Ticket: 356039416, Magic: 18, SL Points: 18, Distance: 0.00018, Symbol: GBPUSD.iux
2025-09-18 15:39:44,868 - DEBUG - debug_print:154 - #356039416: Entry=1.36383, Current=1.36370, SL=1.36365, TP=1.36410
2025-09-18 15:39:44,869 - DEBUG - debug_print:154 - #356039416: point_sl=0.00018, point_be=-1.36400, is_moving_tp=True
2025-09-18 15:39:44,869 - DEBUG - debug_print:154 - #356039416: BUY Moving TP - Current: 1.36370, Trigger: -0.00017
2025-09-18 15:39:44,869 - DEBUG - debug_print:154 - #356039416: BUY Moving TP factor -7577.757549999999 - New SL: -0.00043, New TP: 0.00069
2025-09-18 15:39:44,870 - DEBUG - debug_print:154 - #356039416: Modifying order - SL: 1.36365 -> -0.00043, TP: 1.36410 -> 0.00069
2025-09-18 15:39:44,889 - ERROR - log_error:125 - #356039416 - ERROR in order modification: Order send failed: Invalid stops
NoneType: None
2025-09-18 15:39:44,912 - DEBUG - log_magic_number_info:110 - DEBUG: Magic number usage - Ticket: 356039418, Magic: 18, SL Points: 18, Distance: 0.00018, Symbol: GBPUSD.iux
2025-09-18 15:39:44,915 - DEBUG - debug_print:154 - #356039418: Entry=1.36383, Current=1.36370, SL=1.36365, TP=1.36410
2025-09-18 15:39:44,915 - DEBUG - debug_print:154 - #356039418: point_sl=0.00018, point_be=-1.36400, is_moving_tp=True
2025-09-18 15:39:44,916 - DEBUG - debug_print:154 - #356039418: BUY Moving TP - Current: 1.36370, Trigger: -0.00017
2025-09-18 15:39:44,916 - DEBUG - debug_print:154 - #356039418: BUY Moving TP factor -7577.757549999999 - New SL: -0.00043, New TP: 0.00069
2025-09-18 15:39:44,916 - DEBUG - debug_print:154 - #356039418: Modifying order - SL: 1.36365 -> -0.00043, TP: 1.36410 -> 0.00069
2025-09-18 15:39:44,938 - ERROR - log_error:125 - #356039418 - ERROR in order modification: Order send failed: Invalid stops
NoneType: None
2025-09-18 15:39:45,048 - INFO - log_order_processing:75 - DEBUG: Order processing - Symbol: GBPUSD.iux, Group: , Total: 2, Processed: 2, Action: SL+TP
2025-09-18 15:39:45,049 - DEBUG - debug_print:154 - Processed 2  positions for SL+TP
2025-09-18 15:40:15,112 - DEBUG - debug_print:154 - Processing auto BE/TP for 1 symbols: {'GBPUSD.iux'}
2025-09-18 15:40:15,112 - DEBUG - debug_print:154 - Processing 2 positions for GBPUSD.iux
2025-09-18 15:40:15,113 - DEBUG - debug_print:154 - Processing 2 positions for  (SL: True, TP: True)
2025-09-18 15:40:15,151 - DEBUG - log_magic_number_info:110 - DEBUG: Magic number usage - Ticket: 356039416, Magic: 18, SL Points: 18, Distance: 0.00018, Symbol: GBPUSD.iux
2025-09-18 15:40:15,152 - DEBUG - debug_print:154 - #356039416: Entry=1.36383, Current=1.36370, SL=1.36365, TP=1.36410
2025-09-18 15:40:15,152 - DEBUG - debug_print:154 - #356039416: point_sl=0.00018, point_be=-1.36400, is_moving_tp=True
2025-09-18 15:40:15,153 - DEBUG - debug_print:154 - #356039416: BUY Moving TP - Current: 1.36370, Trigger: -0.00017
2025-09-18 15:40:15,153 - DEBUG - debug_print:154 - #356039416: BUY Moving TP factor -7577.757549999999 - New SL: -0.00043, New TP: 0.00069
2025-09-18 15:40:15,154 - DEBUG - debug_print:154 - #356039416: Modifying order - SL: 1.36365 -> -0.00043, TP: 1.36410 -> 0.00069
2025-09-18 15:40:15,177 - ERROR - log_error:125 - #356039416 - ERROR in order modification: Order send failed: Invalid stops
NoneType: None
2025-09-18 15:40:15,203 - DEBUG - log_magic_number_info:110 - DEBUG: Magic number usage - Ticket: 356039418, Magic: 18, SL Points: 18, Distance: 0.00018, Symbol: GBPUSD.iux
2025-09-18 15:40:15,204 - DEBUG - debug_print:154 - #356039418: Entry=1.36383, Current=1.36370, SL=1.36365, TP=1.36410
2025-09-18 15:40:15,205 - DEBUG - debug_print:154 - #356039418: point_sl=0.00018, point_be=-1.36400, is_moving_tp=True
2025-09-18 15:40:15,205 - DEBUG - debug_print:154 - #356039418: BUY Moving TP - Current: 1.36370, Trigger: -0.00017
2025-09-18 15:40:15,206 - DEBUG - debug_print:154 - #356039418: BUY Moving TP factor -7577.757549999999 - New SL: -0.00043, New TP: 0.00069
2025-09-18 15:40:15,206 - DEBUG - debug_print:154 - #356039418: Modifying order - SL: 1.36365 -> -0.00043, TP: 1.36410 -> 0.00069
2025-09-18 15:40:15,226 - ERROR - log_error:125 - #356039418 - ERROR in order modification: Order send failed: Invalid stops
NoneType: None
2025-09-18 15:40:15,336 - INFO - log_order_processing:75 - DEBUG: Order processing - Symbol: GBPUSD.iux, Group: , Total: 2, Processed: 2, Action: SL+TP
2025-09-18 15:40:15,338 - DEBUG - debug_print:154 - Processed 2  positions for SL+TP
2025-09-18 15:40:45,418 - DEBUG - debug_print:154 - No positions found for auto BE/TP processing
2025-09-18 15:41:15,502 - DEBUG - debug_print:154 - No positions found for auto BE/TP processing
2025-09-18 15:41:45,566 - DEBUG - debug_print:154 - No positions found for auto BE/TP processing
2025-09-18 15:42:15,633 - DEBUG - debug_print:154 - No positions found for auto BE/TP processing
2025-09-18 15:44:27,886 - INFO - log_info:142 - 🟢 Orders processing loop started
2025-09-18 15:44:27,932 - DEBUG - debug_print:154 - No positions found for auto BE/TP processing
2025-09-18 15:44:59,419 - DEBUG - debug_print:154 - No positions found for auto BE/TP processing
2025-09-18 15:45:29,500 - DEBUG - debug_print:154 - No positions found for auto BE/TP processing
2025-09-18 15:45:59,569 - DEBUG - debug_print:154 - No positions found for auto BE/TP processing
2025-09-18 15:46:29,783 - DEBUG - debug_print:154 - No positions found for auto BE/TP processing
2025-09-18 15:46:59,857 - DEBUG - debug_print:154 - No positions found for auto BE/TP processing
